{"kunden": [{"id": "K004", "name": "<PERSON>", "adresse": "Rosenweg 86, 79801 Stuttgart", "telefonnummer": "+49 172 1877222", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM011", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM012", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Sonstiges."}, {"id": "KOM013", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}, {"id": "KOM014", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM015", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT011", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-07-07T09:00:00", "erledigt": false}, {"id": "AKT012", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-07-08T09:00:00", "erledigt": true}]}, {"id": "K005", "name": "<PERSON>", "adresse": "Dorfstraße 83, 24498 Bonn", "telefonnummer": "+49 109 7893412", "email": "anna.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM021", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K006", "name": "<PERSON>", "adresse": "Rosenweg 50, 96390 Bremen", "telefonnummer": "+49 871 1656803", "email": "thomas.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM031", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K007", "name": "<PERSON>", "adresse": "Birkenstraße 45, 63803 Frankfurt", "telefonnummer": "+49 168 2962584", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM041", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Sonstiges."}, {"id": "KOM042", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K008", "name": "<PERSON>", "adresse": "Lindenallee 12, 20836 Dresden", "telefonnummer": "+49 502 5282118", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM051", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via E-Mail."}, {"id": "KOM052", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM053", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM054", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT051", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}, {"id": "AKT052", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "K009", "name": "<PERSON>", "adresse": "Kirchstraße 19, 73293 Hamburg", "telefonnummer": "+49 976 2561265", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM061", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Kundengespräch."}, {"id": "KOM062", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Sonstiges."}, {"id": "KOM063", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}, {"id": "KOM064", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM065", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K010", "name": "<PERSON>", "adresse": "Kirchstraße 40, 29188 Münster", "telefonnummer": "+49 685 8079488", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM071", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}, {"id": "KOM072", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM073", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT071", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-07-01T09:00:00", "erledigt": false}, {"id": "AKT072", "titel": "Produktpräsentation", "beschreibung": "Detaillierte Beschreibung der Aktion: Produktpräsentation", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "K011", "name": "<PERSON>", "adresse": "Gartenstraße 4, 87677 Bochum", "telefonnummer": "+49 566 8795693", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM081", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM082", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K012", "name": "<PERSON>", "adresse": "Schulstraße 89, 65322 Dortmund", "telefonnummer": "+49 935 5125742", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM091", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}, {"id": "KOM092", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM093", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Kundengespräch."}, {"id": "KOM094", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Sonstiges."}, {"id": "KOM095", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K013", "name": "<PERSON>", "adresse": "Bahnhofstraße 20, 12225 Wu<PERSON>tal", "telefonnummer": "+49 874 7403038", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM101", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K014", "name": "<PERSON>", "adresse": "Wiesenweg 89, 36096 Nürnberg", "telefonnummer": "+49 705 9168239", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM111", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}, {"id": "KOM112", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K015", "name": "<PERSON>", "adresse": "Wiesenweg 65, 30947 Dortmund", "telefonnummer": "+49 668 5916799", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM121", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K016", "name": "<PERSON>", "adresse": "Wiesenweg 68, 59634 Frankfurt", "telefonnummer": "+49 124 9047886", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM131", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Sonstiges."}, {"id": "KOM132", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM133", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM134", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT131", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-07-02T09:00:00", "erledigt": false}]}, {"id": "K017", "name": "<PERSON>", "adresse": "Wiesenweg 11, 83092 Bremen", "telefonnummer": "+49 269 9324224", "email": "anna.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM141", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM142", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM143", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT141", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-07-05T09:00:00", "erledigt": false}, {"id": "AKT142", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rabatt anbieten", "faelligkeitsDatumZeit": "2025-07-08T09:00:00", "erledigt": false}]}, {"id": "K018", "name": "<PERSON>", "adresse": "Dorfstraße 5, 39445 Bonn", "telefonnummer": "+49 183 4555072", "email": "max.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM151", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K019", "name": "<PERSON>", "adresse": "Birkenstraße 18, 99174 Bochum", "telefonnummer": "+49 871 2768086", "email": "andreas.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM161", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}, {"id": "KOM162", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM163", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM164", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT161", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-06-16T09:00:00", "erledigt": false}, {"id": "AKT162", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-27T09:00:00", "erledigt": false}]}, {"id": "K020", "name": "<PERSON>", "adresse": "Eichenweg 54, 36675 Berlin", "telefonnummer": "+49 529 8986065", "email": "sandra.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM171", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM172", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT171", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": false}]}, {"id": "K021", "name": "<PERSON>", "adresse": "Hauptstraße 12, 60181 Bonn", "telefonnummer": "+49 886 3767104", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM181", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K022", "name": "<PERSON>", "adresse": "Schulstraße 19, 30540 Hannover", "telefonnummer": "+49 831 2797612", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM191", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM192", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K023", "name": "<PERSON>", "adresse": "Parkstraße 45, 86510 Bielefeld", "telefonnummer": "+49 210 4411765", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM201", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT201", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-06T09:00:00", "erledigt": false}]}, {"id": "K024", "name": "<PERSON>", "adresse": "Bergstraße 97, 33281 Köln", "telefonnummer": "+49 954 1558319", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM211", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}, {"id": "KOM212", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM213", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}, {"id": "KOM214", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Sonstiges."}, {"id": "KOM215", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT211", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rabatt anbieten", "faelligkeitsDatumZeit": "2025-06-28T09:00:00", "erledigt": false}, {"id": "AKT212", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-06-14T09:00:00", "erledigt": false}]}, {"id": "K025", "name": "<PERSON>", "adresse": "Bahnhofstraße 77, 79058 Bochum", "telefonnummer": "+49 914 4284243", "email": "christina.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM221", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Garantiefall."}, {"id": "KOM222", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}, {"id": "KOM223", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM224", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K026", "name": "<PERSON>", "adresse": "Birkenstraße 58, 70937 Berlin", "telefonnummer": "+49 919 6114222", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM231", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM232", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Garantiefall."}, {"id": "KOM233", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM234", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}, {"id": "KOM235", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K027", "name": "<PERSON>", "adresse": "Rosenweg 11, 58004 München", "telefonnummer": "+49 523 3620480", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM241", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}, {"id": "KOM242", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K028", "name": "<PERSON>", "adresse": "Birkenstraße 50, 67643 Bremen", "telefonnummer": "+49 310 9483026", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM251", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM252", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Telefon."}, {"id": "KOM253", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K029", "name": "<PERSON>", "adresse": "Bahnhofstraße 63, 54992 Düsseldorf", "telefonnummer": "+49 170 7602708", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM261", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Sonstiges."}, {"id": "KOM262", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Kundengespräch."}, {"id": "KOM263", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM264", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}, {"id": "KOM265", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K030", "name": "<PERSON>", "adresse": "Eichenweg 6, 76188 Dortmund", "telefonnummer": "+49 457 2625023", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM271", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K031", "name": "<PERSON><PERSON><PERSON>", "adresse": "Dorfstraße 71, 28051 Hamburg", "telefonnummer": "+49 861 6852225", "email": "katharina.schrö***************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM281", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Kundengespräch."}, {"id": "KOM282", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K032", "name": "<PERSON>", "adresse": "Bergstraße 49, 19654 Stuttgart", "telefonnummer": "+49 559 5210346", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM291", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT291", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-07-02T09:00:00", "erledigt": true}, {"id": "AKT292", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": true}]}, {"id": "K033", "name": "<PERSON>", "adresse": "Kirchstraße 62, 55654 Duisburg", "telefonnummer": "+49 316 9160389", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM301", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Sonstiges."}, {"id": "KOM302", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}, {"id": "KOM303", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM304", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM305", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K034", "name": "<PERSON>", "adresse": "Schulstraße 63, 16903 Köln", "telefonnummer": "+49 966 6109827", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM311", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Sonstiges."}, {"id": "KOM312", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K035", "name": "<PERSON>", "adresse": "Birkenstraße 57, 11811 Köln", "telefonnummer": "+49 809 8068131", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM321", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM002", "datum": "2025-06-10T22:44:23", "typ": "E-Mail", "beschreibung": "bvc", "notizen": "bc"}], "aktionen": []}, {"id": "K036", "name": "<PERSON>", "adresse": "Rosenweg 27, 48283 Duisburg", "telefonnummer": "+49 528 8677606", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM331", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Kundengespräch."}, {"id": "KOM332", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM333", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT331", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-03T09:00:00", "erledigt": false}]}, {"id": "K037", "name": "<PERSON>", "adresse": "Bahnhofstraße 96, 15380 Leipzig", "telefonnummer": "+49 249 9513268", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM341", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Garantiefall."}, {"id": "KOM342", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT341", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-07T09:00:00", "erledigt": false}, {"id": "AKT342", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-29T09:00:00", "erledigt": true}]}, {"id": "K038", "name": "<PERSON>", "adresse": "Schulstraße 34, 50753 Nürnberg", "telefonnummer": "+49 247 1166348", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM351", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K039", "name": "<PERSON>", "adresse": "Parkstraße 32, 12237 München", "telefonnummer": "+49 813 3243197", "email": "max.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM361", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM362", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Sonstiges."}, {"id": "KOM363", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM364", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}, {"id": "KOM365", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT361", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": true}]}, {"id": "K040", "name": "<PERSON>", "adresse": "Gartenstraße 14, 81967 Köln", "telefonnummer": "+49 841 8992597", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM371", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM372", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}, {"id": "KOM373", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}, {"id": "KOM374", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K041", "name": "<PERSON>", "adresse": "Birkenstraße 63, 66850 Essen", "telefonnummer": "+49 499 4242854", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM381", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM382", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}, {"id": "KOM383", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT381", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-19T09:00:00", "erledigt": true}, {"id": "AKT382", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rabatt anbieten", "faelligkeitsDatumZeit": "2025-07-03T09:00:00", "erledigt": false}]}, {"id": "K042", "name": "<PERSON>", "adresse": "Eichenweg 36, 25858 <PERSON><PERSON><PERSON>", "telefonnummer": "+49 942 2439317", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM391", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via E-Mail."}, {"id": "KOM392", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM393", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT391", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rabatt anbieten", "faelligkeitsDatumZeit": "2025-06-28T09:00:00", "erledigt": false}]}, {"id": "K043", "name": "<PERSON>", "adresse": "Tannenweg 100, 64414 Münster", "telefonnummer": "+49 841 9094772", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM401", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM402", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM403", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Garantiefall."}, {"id": "KOM404", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT401", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-07-08T09:00:00", "erledigt": false}, {"id": "AKT402", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-07-01T09:00:00", "erledigt": false}]}, {"id": "K044", "name": "<PERSON><PERSON><PERSON>", "adresse": "Bergstraße 6, 77279 Münster", "telefonnummer": "+49 415 4751630", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM411", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}, {"id": "KOM412", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}, {"id": "KOM413", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}, {"id": "KOM414", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K045", "name": "<PERSON>", "adresse": "Rosenweg 21, 23312 Duisburg", "telefonnummer": "+49 246 1943201", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM421", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K046", "name": "<PERSON>", "adresse": "Wiesenweg 14, 97523 Dresden", "telefonnummer": "+49 135 8833502", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM431", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via E-Mail."}, {"id": "KOM432", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Sonstiges."}, {"id": "KOM433", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM434", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}, {"id": "KOM435", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT431", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-07-03T09:00:00", "erledigt": false}]}, {"id": "K047", "name": "<PERSON>", "adresse": "Rosenweg 17, 68584 Düsseldorf", "telefonnummer": "+49 341 8534878", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM441", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}, {"id": "KOM442", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Kundengespräch."}, {"id": "KOM443", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Kundengespräch."}, {"id": "KOM444", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT441", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "K048", "name": "<PERSON>", "adresse": "Bergstraße 43, 57537 Hannover", "telefonnummer": "+49 862 5869176", "email": "lisa.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM451", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via E-Mail."}, {"id": "KOM452", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K049", "name": "<PERSON>", "adresse": "Schulstraße 67, 38740 Köln", "telefonnummer": "+49 846 3143869", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM461", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM462", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM463", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K050", "name": "<PERSON>", "adresse": "Dorfstraße 55, 55876 Bochum", "telefonnummer": "+49 284 4811893", "email": "christina.sch<PERSON><EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM471", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM472", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}, {"id": "KOM473", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K051", "name": "<PERSON>", "adresse": "Eichenweg 86, 77030 Hamburg", "telefonnummer": "+49 397 7446713", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM481", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM482", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM483", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}, {"id": "KOM484", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}, {"id": "KOM485", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT481", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-06-14T09:00:00", "erledigt": true}, {"id": "AKT482", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-25T09:00:00", "erledigt": false}]}, {"id": "K052", "name": "<PERSON>", "adresse": "Wiesenweg 68, 66239 Düsseldorf", "telefonnummer": "+49 718 1390964", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM491", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM492", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM493", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM494", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT491", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": true}, {"id": "AKT492", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-07-03T09:00:00", "erledigt": false}]}, {"id": "K053", "name": "<PERSON>", "adresse": "Rosenweg 21, 41431 Köln", "telefonnummer": "+49 929 2811050", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM501", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM502", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM503", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Sonstiges."}, {"id": "KOM504", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K054", "name": "<PERSON>", "adresse": "Parkstraße 15, 95116 Essen", "telefonnummer": "+49 997 7366744", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM511", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K055", "name": "<PERSON>", "adresse": "Lindenallee 40, 45104 Dortmund", "telefonnummer": "+49 795 6724494", "email": "anna.schr<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM521", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}, {"id": "KOM522", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM523", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}, {"id": "KOM524", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT521", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-07-08T09:00:00", "erledigt": true}, {"id": "AKT522", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "K056", "name": "<PERSON>", "adresse": "Bergstraße 51, 80828 K<PERSON>ln", "telefonnummer": "+49 210 3015393", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM531", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM532", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K057", "name": "<PERSON>", "adresse": "Eichenweg 18, 11590 Dresden", "telefonnummer": "+49 422 8771955", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM541", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K058", "name": "<PERSON>", "adresse": "Bahnhofstraße 56, 94816 Duisburg", "telefonnummer": "+49 359 3356716", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM551", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Kundengespräch."}, {"id": "KOM552", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Telefon."}, {"id": "KOM553", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Telefon."}, {"id": "KOM554", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM555", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT551", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-06-25T09:00:00", "erledigt": true}, {"id": "AKT552", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-07-09T09:00:00", "erledigt": false}]}, {"id": "K059", "name": "<PERSON>", "adresse": "Waldstraße 32, 42077 Köln", "telefonnummer": "+49 801 9884039", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM561", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via E-Mail."}, {"id": "KOM562", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via E-Mail."}, {"id": "KOM563", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT561", "titel": "Produktpräsentation", "beschreibung": "Detaillierte Beschreibung der Aktion: Produktpräsentation", "faelligkeitsDatumZeit": "2025-07-02T09:00:00", "erledigt": true}]}, {"id": "K060", "name": "<PERSON>", "adresse": "Tannenweg 21, 94598 München", "telefonnummer": "+49 590 7597920", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM571", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM572", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}, {"id": "KOM573", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via E-Mail."}, {"id": "KOM574", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT571", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-09T09:00:00", "erledigt": false}]}, {"id": "K061", "name": "<PERSON>", "adresse": "Lindenallee 35, 22372 Dortmund", "telefonnummer": "+49 817 2994570", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM581", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Sonstiges."}, {"id": "KOM582", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}, {"id": "KOM583", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM584", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K062", "name": "<PERSON>", "adresse": "Wiesenweg 9, 85537 Stuttgart", "telefonnummer": "+49 450 4141151", "email": "lisa.hoff<PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM591", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT591", "titel": "Produktpräsentation", "beschreibung": "Detaillierte Beschreibung der Aktion: Produktpräsentation", "faelligkeitsDatumZeit": "2025-07-05T09:00:00", "erledigt": false}]}, {"id": "K063", "name": "<PERSON>", "adresse": "Bahnhofstraße 40, 32159 Dortmund", "telefonnummer": "+49 278 7571462", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM601", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K064", "name": "<PERSON>", "adresse": "Waldstraße 4, 71785 Hamburg", "telefonnummer": "+49 617 3504220", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM611", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K065", "name": "<PERSON>", "adresse": "Kirchstraße 20, 15397 <PERSON><PERSON><PERSON>", "telefonnummer": "+49 590 2262482", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM621", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM622", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM623", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT621", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-07-09T09:00:00", "erledigt": true}, {"id": "AKT622", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-06-30T09:00:00", "erledigt": false}]}, {"id": "K066", "name": "<PERSON>", "adresse": "Kirchstraße 1, 55581 Berlin", "telefonnummer": "+49 380 9649812", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM631", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}, {"id": "KOM632", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM633", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT631", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": false}]}, {"id": "K067", "name": "<PERSON>", "adresse": "Lindenallee 61, 11915 Duisburg", "telefonnummer": "+49 816 1875080", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM641", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM642", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Telefon."}, {"id": "KOM643", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT641", "titel": "Produktpräsentation", "beschreibung": "Detaillierte Beschreibung der Aktion: Produktpräsentation", "faelligkeitsDatumZeit": "2025-07-06T09:00:00", "erledigt": false}, {"id": "AKT642", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}]}, {"id": "K068", "name": "<PERSON>", "adresse": "Hauptstraße 62, 48507 München", "telefonnummer": "+49 766 8069344", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM651", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM652", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K069", "name": "<PERSON>", "adresse": "Hauptstraße 3, 31763 Köln", "telefonnummer": "+49 950 9649069", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM661", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM662", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM663", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM664", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT661", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-26T09:00:00", "erledigt": false}]}, {"id": "K070", "name": "<PERSON>", "adresse": "Hauptstraße 50, 46121 Hannover", "telefonnummer": "+49 639 7716245", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM671", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT671", "titel": "<PERSON><PERSON><PERSON> vere<PERSON>en", "beschreibung": "Detaillierte Beschreibung der Aktion: Te<PERSON>in vereinbaren", "faelligkeitsDatumZeit": "2025-07-02T09:00:00", "erledigt": false}, {"id": "AKT672", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-26T09:00:00", "erledigt": false}]}, {"id": "K071", "name": "<PERSON>", "adresse": "Kirchstraße 82, 41183 Nürnberg", "telefonnummer": "+49 192 2134957", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM681", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Garantiefall."}, {"id": "KOM682", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM683", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K072", "name": "<PERSON>", "adresse": "Gartenstraße 80, 29359 Düsseldorf", "telefonnummer": "+49 567 9974238", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM691", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}, {"id": "KOM692", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Garantiefall."}, {"id": "KOM693", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM694", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}, {"id": "KOM695", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT691", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": true}]}, {"id": "K073", "name": "<PERSON>", "adresse": "Bahnhofstraße 93, 54323 Frankfurt", "telefonnummer": "+49 767 2451133", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM701", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM702", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Sonstiges."}, {"id": "KOM703", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM704", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Sonstiges."}, {"id": "KOM705", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K074", "name": "<PERSON>", "adresse": "Rosenweg 3, 72284 München", "telefonnummer": "+49 513 6943267", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM711", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT711", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-06-19T09:00:00", "erledigt": false}, {"id": "AKT712", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "K075", "name": "<PERSON>", "adresse": "Gartenstraße 8, 89065 Duisburg", "telefonnummer": "+49 992 4715422", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM721", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT721", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-06-27T09:00:00", "erledigt": true}, {"id": "AKT722", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-07-06T09:00:00", "erledigt": true}]}, {"id": "K076", "name": "<PERSON>", "adresse": "Bergstraße 43, 31330 Düsseldorf", "telefonnummer": "+49 849 6063889", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM731", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM732", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM733", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM734", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM735", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT731", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-07-02T09:00:00", "erledigt": false}, {"id": "AKT732", "titel": "Beschwerde bearbeiten", "beschreibung": "Detaillierte Beschreibung der Aktion: Beschwerde bearbeiten", "faelligkeitsDatumZeit": "2025-06-14T09:00:00", "erledigt": false}]}, {"id": "K077", "name": "<PERSON>", "adresse": "Parkstraße 37, 83012 Dortmund", "telefonnummer": "+49 965 2541943", "email": "lisa.sch<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM741", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM742", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM743", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}, {"id": "KOM744", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K078", "name": "<PERSON>", "adresse": "Wiesenweg 21, 27564 Essen", "telefonnummer": "+49 575 6552040", "email": "daniel.sch<PERSON><EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM751", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Kundengespräch."}], "aktionen": [{"id": "AKT751", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-28T09:00:00", "erledigt": false}]}, {"id": "K079", "name": "<PERSON>", "adresse": "Lindenallee 93, 23173 Leipzig", "telefonnummer": "+49 973 8581479", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM761", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}, {"id": "KOM762", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM763", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM764", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Garantiefall."}, {"id": "KOM765", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K080", "name": "<PERSON>", "adresse": "Kirchstraße 73, 61188 Essen", "telefonnummer": "+49 577 9836744", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM771", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K081", "name": "<PERSON>", "adresse": "Birkenstraße 44, 25260 Leipzig", "telefonnummer": "+49 880 9183403", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM781", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}, {"id": "KOM782", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K082", "name": "<PERSON>", "adresse": "Rosenweg 24, 10190 Bochum", "telefonnummer": "+49 881 6450319", "email": "christina.schrö***************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM791", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Kundengespräch."}, {"id": "KOM792", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}, {"id": "KOM793", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}, {"id": "KOM794", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM795", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT791", "titel": "<PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Vertrag zusenden", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "K083", "name": "<PERSON>", "adresse": "Schulstraße 62, 29493 Dortmund", "telefonnummer": "+49 350 5099697", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM801", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K084", "name": "<PERSON>", "adresse": "Gartenstraße 37, 93536 <PERSON><PERSON><PERSON>", "telefonnummer": "+49 688 8723549", "email": "christina.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM811", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM812", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}, {"id": "KOM813", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT811", "titel": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Aktion: Rückruf", "faelligkeitsDatumZeit": "2025-06-27T09:00:00", "erledigt": true}]}, {"id": "K085", "name": "<PERSON>", "adresse": "Tannenweg 90, 45149 Köln", "telefonnummer": "+49 559 1667966", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM821", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}, {"id": "KOM822", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM823", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM824", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM825", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K086", "name": "<PERSON>", "adresse": "Waldstraße 98, 55410 Essen", "telefonnummer": "+49 440 6228477", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM831", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}, {"id": "KOM832", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K087", "name": "<PERSON>", "adresse": "Schulstraße 77, 55689 Stuttgart", "telefonnummer": "+49 675 2073691", "email": "max.mü****************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM841", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM842", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via Kundengespräch."}, {"id": "KOM843", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}, {"id": "KOM844", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via E-Mail."}], "aktionen": []}, {"id": "K088", "name": "<PERSON>", "adresse": "Birkenstraße 64, 24744 Stuttgart", "telefonnummer": "+49 464 1684416", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM851", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM852", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM853", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Sonstiges."}, {"id": "KOM854", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Reklamation", "notizen": "Detaillierte Beschreibung der Reklamation-Kommunikation via E-Mail."}, {"id": "KOM855", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K089", "name": "<PERSON>", "adresse": "Eichenweg 60, 24726 Dresden", "telefonnummer": "+49 354 6872381", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM861", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Sonstiges."}, {"id": "KOM862", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Kundengespräch."}, {"id": "KOM863", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K090", "name": "<PERSON><PERSON><PERSON>", "adresse": "Gartenstraße 17, 17403 Dortmund", "telefonnummer": "+49 123 6840162", "email": "katharina.schä***************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM871", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}, {"id": "KOM872", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via E-Mail."}, {"id": "KOM873", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Garantiefall."}, {"id": "KOM874", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K091", "name": "<PERSON>", "adresse": "Lindenallee 37, 62298 Hannover", "telefonnummer": "+49 236 2503412", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM881", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Garantiefall."}, {"id": "KOM882", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM883", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via E-Mail."}, {"id": "KOM884", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}], "aktionen": [{"id": "AKT881", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-07T09:00:00", "erledigt": false}]}, {"id": "K092", "name": "<PERSON>", "adresse": "Eichenweg 18, 68214 Münster", "telefonnummer": "+49 762 7331944", "email": "laura.schä***************", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM891", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K093", "name": "<PERSON>", "adresse": "Parkstraße 19, 40888 <PERSON><PERSON><PERSON>", "telefonnummer": "+49 499 3201649", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM901", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM902", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM903", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Telefon."}, {"id": "KOM904", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}, {"id": "KOM905", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}], "aktionen": [{"id": "AKT901", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}, {"id": "AKT902", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": true}]}, {"id": "K094", "name": "<PERSON>", "adresse": "Lindenallee 47, 63492 Leipzig", "telefonnummer": "+49 414 1172590", "email": "thomas.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM911", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Kundengespräch."}, {"id": "KOM912", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K095", "name": "<PERSON>", "adresse": "Rosenweg 1, 64416 Münster", "telefonnummer": "+49 487 3447742", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM921", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via E-Mail."}, {"id": "KOM922", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM923", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K096", "name": "<PERSON>", "adresse": "Hauptstraße 78, 55276 Duisburg", "telefonnummer": "+49 260 8378868", "email": "sarah.z<PERSON><PERSON>@example.com", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM931", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Telefon."}, {"id": "KOM932", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K097", "name": "<PERSON>", "adresse": "Birkenstraße 32, 12716 Köln", "telefonnummer": "+49 660 9650277", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM941", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Kundengespräch."}, {"id": "KOM942", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Kundengespräch."}, {"id": "KOM943", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Sonstiges."}, {"id": "KOM944", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "<PERSON><PERSON><PERSON>", "notizen": "Detaillierte Beschreibung der Feedback-Kommunikation via Sonstiges."}, {"id": "KOM945", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "K098", "name": "<PERSON>", "adresse": "Bahnhofstraße 64, 99754 Bochum", "telefonnummer": "+49 295 9048779", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM951", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via E-Mail."}, {"id": "KOM952", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}], "aktionen": []}, {"id": "K099", "name": "<PERSON>", "adresse": "Bergstraße 28, 64281 Münster", "telefonnummer": "+49 611 2232017", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM961", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM962", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Sonstiges."}, {"id": "KOM963", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Telefon."}, {"id": "KOM964", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Telefon."}, {"id": "KOM965", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via E-Mail."}], "aktionen": [{"id": "AKT961", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-07-09T09:00:00", "erledigt": true}, {"id": "AKT962", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}]}, {"id": "K100", "name": "<PERSON>", "adresse": "Parkstraße 2, 77415 Duisburg", "telefonnummer": "+49 221 6301485", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM971", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via E-Mail."}, {"id": "KOM972", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Kundengespräch."}, {"id": "KOM973", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Produktvorstellung", "notizen": "Detaillierte Beschreibung der Produktvorstellung-Kommunikation via Sonstiges."}, {"id": "KOM974", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}], "aktionen": [{"id": "AKT971", "titel": "<PERSON><PERSON> zusenden", "beschreibung": "Detaillierte Beschreibung der Aktion: Informationen zusenden", "faelligkeitsDatumZeit": "2025-07-05T09:00:00", "erledigt": false}, {"id": "AKT972", "titel": "Kundenbindungsmaßnahme", "beschreibung": "Detaillierte Beschreibung der Aktion: Kundenbindungsmaßnahme", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "K101", "name": "<PERSON>", "adresse": "Birkenstraße 78, 37929 Köln", "telefonnummer": "+49 958 7801036", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM981", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vertragsverhandlung", "notizen": "Detaillierte Beschreibung der Vertragsverhandlung-Kommunikation via Garantiefall."}, {"id": "KOM982", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Beratungsgespräch", "notizen": "Detaillierte Beschreibung der Beratungsgespräch-Kommunikation via Garantiefall."}, {"id": "KOM983", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Erstkontakt", "notizen": "Detaillierte Beschreibung der Erstkontakt-Kommunikation via Kundengespräch."}, {"id": "KOM984", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}, {"id": "KOM985", "datum": "2025-06-10T21:43:28", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Nachfrage", "notizen": "Detaillierte Beschreibung der Nachfrage-Kommunikation via Garantiefall."}], "aktionen": []}, {"id": "K102", "name": "<PERSON>", "adresse": "Eichenweg 33, 84770 Hamburg", "telefonnummer": "+49 707 2179077", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM991", "datum": "2025-06-10T21:43:28", "typ": "E-Mail", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via E-Mail."}, {"id": "KOM992", "datum": "2025-06-10T21:43:28", "typ": "Sonstiges", "beschreibung": "Support", "notizen": "Detaillierte Beschreibung der Support-Kommunikation via Sonstiges."}], "aktionen": []}, {"id": "K103", "name": "<PERSON>", "adresse": "Tannenweg 56, 36145 Berlin", "telefonnummer": "+49 891 9367072", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [{"id": "KOM1001", "datum": "2025-06-10T21:43:28", "typ": "Telefon", "beschreibung": "Produktanfrage", "notizen": "Detaillierte Beschreibung der Produktanfrage-Kommunikation via Telefon."}, {"id": "KOM1002", "datum": "2025-06-10T21:43:28", "typ": "Kundengespräch", "beschreibung": "Beschwerde", "notizen": "Detaillierte Beschreibung der Beschwerde-Kommunikation via Kundengespräch."}], "aktionen": []}, {"id": "I001", "name": "<PERSON>", "adresse": "Ahornweg 95, 75816 Lü<PERSON>", "telefonnummer": "+49 841 2304146", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK011", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA011", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}]}, {"id": "I002", "name": "<PERSON>", "adresse": "Kastanienallee 96, 40291 Aachen", "telefonnummer": "+49 814 9236338", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK021", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK022", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA021", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}, {"id": "IA022", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-20T09:00:00", "erledigt": false}]}, {"id": "I003", "name": "<PERSON>", "adresse": "Platanenhof 100, 76064 Regensburg", "telefonnummer": "+49 471 9235255", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK031", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA031", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": false}, {"id": "IA032", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "I004", "name": "<PERSON>", "adresse": "Eschenweg 77, 49011 Augsburg", "telefonnummer": "+49 556 9827199", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK041", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK042", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA041", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}, {"id": "IA042", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I005", "name": "<PERSON><PERSON>", "adresse": "Kastanienallee 41, 69619 Rostock", "telefonnummer": "+49 755 7694196", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK051", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK052", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Kundengespräch."}], "aktionen": [{"id": "IA051", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "I006", "name": "<PERSON>", "adresse": "Zedernweg 92, 32840 Heidelberg", "telefonnummer": "+49 182 4492170", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK061", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK062", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Kundengespräch."}], "aktionen": [{"id": "IA061", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": true}, {"id": "IA062", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I007", "name": "<PERSON>", "adresse": "Zedernweg 21, 44295 Chemnitz", "telefonnummer": "+49 237 1416611", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK071", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Kundengespräch."}], "aktionen": [{"id": "IA071", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": false}, {"id": "IA072", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": false}]}, {"id": "I008", "name": "<PERSON>", "adresse": "Akazienstraße 100, 34430 Lübeck", "telefonnummer": "+49 434 2195312", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK081", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK082", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK083", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA081", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": false}, {"id": "IA082", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": false}]}, {"id": "I009", "name": "<PERSON>", "adresse": "Magnolienstraße 5, 72411 Mainz", "telefonnummer": "+49 943 7591588", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK091", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Preisanfrage", "notizen": "Detaillierte Beschreibung der Preisanfrage-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK092", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK093", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA091", "titel": "<PERSON>rstgespr<PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Erstgespräch", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": false}]}, {"id": "I010", "name": "<PERSON>", "adresse": "Lärchenweg 66, 59507 Trier", "telefonnummer": "+49 348 6794525", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK101", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA101", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}]}, {"id": "I011", "name": "<PERSON><PERSON>", "adresse": "Weidenweg 99, 84363 Freiburg", "telefonnummer": "+49 723 3363639", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK111", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK112", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA111", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-19T09:00:00", "erledigt": false}]}, {"id": "I012", "name": "<PERSON>", "adresse": "Buchenstraße 21, 53104 Chemnitz", "telefonnummer": "+49 669 3455154", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK121", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Preisanfrage", "notizen": "Detaillierte Beschreibung der Preisanfrage-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK122", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA121", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": false}]}, {"id": "I013", "name": "<PERSON><PERSON><PERSON>", "adresse": "Kastanienallee 77, 71122 Würzburg", "telefonnummer": "+49 481 8256206", "email": "jürgen<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK131", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK132", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA131", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I014", "name": "<PERSON>", "adresse": "Erlenweg 64, 13825 Potsdam", "telefonnummer": "+49 515 9242265", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK141", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK142", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "KOM003", "datum": "2025-06-23T17:15:42", "typ": "Erledigte Aktion", "beschreibung": "Aktion abgeschlossen: <PERSON><PERSON> zu<PERSON>den", "notizen": "Aktion mit ID IA142 wurde als erledigt markiert."}], "aktionen": [{"id": "IA141", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-20T09:00:00", "erledigt": false}, {"id": "IA142", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": true}]}, {"id": "I015", "name": "<PERSON>", "adresse": "Akazienstraße 33, 87084 Braunschweig", "telefonnummer": "+49 405 9583885", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK151", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK152", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK153", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA151", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": true}]}, {"id": "I016", "name": "<PERSON><PERSON><PERSON>", "adresse": "Kiefernweg 29, 22548 Göttingen", "telefonnummer": "+49 950 5816107", "email": "jürgen<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK161", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK162", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK163", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA161", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": false}, {"id": "IA162", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I017", "name": "<PERSON>", "adresse": "Fichtenstraße 21, 17000 Ulm", "telefonnummer": "+49 972 2868748", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK171", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK172", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA171", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-19T09:00:00", "erledigt": false}]}, {"id": "I018", "name": "<PERSON><PERSON>", "adresse": "Magnolienstraße 73, 18303 Ulm", "telefonnummer": "+49 540 4791165", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK181", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK182", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA181", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}]}, {"id": "I019", "name": "<PERSON>", "adresse": "Kastanienallee 67, 96752 Potsdam", "telefonnummer": "+49 431 1598172", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK191", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK192", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK193", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA191", "titel": "<PERSON>rstgespr<PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Erstgespräch", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I020", "name": "<PERSON>", "adresse": "Fichtenstraße 95, 82364 Aachen", "telefonnummer": "+49 411 9445189", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK201", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK202", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK203", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Kundengespräch."}], "aktionen": [{"id": "IA201", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": false}, {"id": "IA202", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "I021", "name": "<PERSON>", "adresse": "Akazienstraße 82, 63565 Saarbrücken", "telefonnummer": "+49 948 3831420", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK211", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Preisanfrage", "notizen": "Detaillierte Beschreibung der Preisanfrage-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA211", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-16T09:00:00", "erledigt": false}, {"id": "IA212", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I022", "name": "<PERSON>", "adresse": "Fichtenstraße 89, 72481 Aachen", "telefonnummer": "+49 852 8354419", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK221", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK222", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA221", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": false}, {"id": "IA222", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-19T09:00:00", "erledigt": false}]}, {"id": "I023", "name": "<PERSON>", "adresse": "Akazienstraße 88, 83057 Aachen", "telefonnummer": "+49 105 2938144", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK231", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK232", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA231", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}, {"id": "IA232", "titel": "<PERSON>rstgespr<PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Erstgespräch", "faelligkeitsDatumZeit": "2025-06-18T09:00:00", "erledigt": false}]}, {"id": "I024", "name": "<PERSON><PERSON><PERSON>", "adresse": "Erlenweg 52, 69985 Karlsruhe", "telefonnummer": "+49 230 6409600", "email": "jürgen<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK241", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK242", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA241", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": true}, {"id": "IA242", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": false}]}, {"id": "I025", "name": "<PERSON>", "adresse": "Weidenweg 43, 15738 Magdeburg", "telefonnummer": "+49 378 7707893", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK251", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA251", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": true}]}, {"id": "I026", "name": "<PERSON>", "adresse": "Ahornweg 5, 72275 Mainz", "telefonnummer": "+49 728 3391563", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK261", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA261", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}, {"id": "IA262", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": true}]}, {"id": "I027", "name": "<PERSON>", "adresse": "Fichtenstraße 69, 27539 Göttingen", "telefonnummer": "+49 387 2208068", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK271", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK272", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA271", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": false}]}, {"id": "I028", "name": "<PERSON>", "adresse": "Ulmenstraße 80, 77402 Ulm", "telefonnummer": "+49 245 7194831", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK281", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK282", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK283", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "KOM004", "datum": "2025-06-23T17:16:03", "typ": "Erledigte Aktion", "beschreibung": "Aktion abgeschlossen: Erstgespräch", "notizen": "Aktion mit ID IA281 wurde als erledigt markiert."}], "aktionen": [{"id": "IA281", "titel": "<PERSON>rstgespr<PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Erstgespräch", "faelligkeitsDatumZeit": "2025-06-21T09:00:00", "erledigt": true}]}, {"id": "I029", "name": "<PERSON>", "adresse": "Ahornweg 82, 84076 Würzburg", "telefonnummer": "+49 750 9619351", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK291", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA291", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "I030", "name": "<PERSON>", "adresse": "Buchenstraße 31, 39189 Mainz", "telefonnummer": "+49 994 5213371", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK301", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA301", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}]}, {"id": "I031", "name": "<PERSON>", "adresse": "Kiefernweg 72, 21131 Freiburg", "telefonnummer": "+49 855 1656885", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK311", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK312", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK313", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA311", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "I032", "name": "<PERSON>", "adresse": "Zedernweg 69, 34822 Regensburg", "telefonnummer": "+49 857 1255881", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK321", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK322", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK323", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA321", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-20T09:00:00", "erledigt": false}, {"id": "IA322", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "I033", "name": "<PERSON>", "adresse": "Lärchenweg 2, 67634 Braun<PERSON>weig", "telefonnummer": "+49 298 6024318", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK331", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA331", "titel": "Bedarfsanaly<PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Bedarfsanalyse", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}]}, {"id": "I034", "name": "<PERSON><PERSON>", "adresse": "Kastanienallee 6, 32111 Potsdam", "telefonnummer": "+49 387 8800421", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK341", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK342", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA341", "titel": "Produktmuster senden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktmuster senden", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}, {"id": "IA342", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}]}, {"id": "I035", "name": "<PERSON>", "adresse": "Pappelstraße 23, 29505 Kiel", "telefonnummer": "+49 992 3081969", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK351", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via E-Mail."}], "aktionen": [{"id": "IA351", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": true}]}, {"id": "I036", "name": "<PERSON>", "adresse": "Platanenhof 16, 99916 Freiburg", "telefonnummer": "+49 152 1940815", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK361", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA361", "titel": "Produktvorstellung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Produktvorstellung", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": false}]}, {"id": "I037", "name": "<PERSON>", "adresse": "Lärchenweg 90, 66296 Rostock", "telefonnummer": "+49 387 4063377", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK371", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA371", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}, {"id": "IA372", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}]}, {"id": "I038", "name": "<PERSON><PERSON><PERSON>", "adresse": "Ulmenstraße 31, 19561 Rostock", "telefonnummer": "+49 639 6829728", "email": "jürgen<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK381", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK382", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Informationsanfrage", "notizen": "Detaillierte Beschreibung der Informationsanfrage-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK383", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA381", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}, {"id": "IA382", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "I039", "name": "<PERSON>", "adresse": "Fichtenstraße 47, 56797 Braunschweig", "telefonnummer": "+49 467 3429226", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK391", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK392", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Kundengespräch."}], "aktionen": [{"id": "IA391", "titel": "Katalog zusenden", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: <PERSON><PERSON> zu<PERSON>den", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}]}, {"id": "I040", "name": "<PERSON><PERSON>", "adresse": "Ulmenstraße 11, 82140 Göttingen", "telefonnummer": "+49 573 2438736", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK401", "datum": "2025-06-10T21:43:29", "typ": "Kundengespräch", "beschreibung": "Preisanfrage", "notizen": "Detaillierte Beschreibung der Preisanfrage-Kommunikation mit dem Interessenten via Kundengespräch."}, {"id": "IK402", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA401", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-14T09:00:00", "erledigt": false}]}, {"id": "I041", "name": "<PERSON>", "adresse": "Eschenweg 71, 41377 Mainz", "telefonnummer": "+49 533 8462474", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK411", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK412", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK413", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA411", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": false}]}, {"id": "I042", "name": "<PERSON>", "adresse": "Lärchenweg 70, 13343 Regensburg", "telefonnummer": "+49 433 5335550", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK421", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK422", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA421", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-23T09:00:00", "erledigt": false}]}, {"id": "I043", "name": "<PERSON><PERSON>", "adresse": "Erlenweg 92, 92971 Chemnitz", "telefonnummer": "+49 677 1138637", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK431", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA431", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-17T09:00:00", "erledigt": false}, {"id": "IA432", "titel": "Preisverhandlung", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Preisverhandlung", "faelligkeitsDatumZeit": "2025-06-13T09:00:00", "erledigt": false}]}, {"id": "I044", "name": "<PERSON>", "adresse": "Eschenweg 7, 72905 Potsdam", "telefonnummer": "+49 709 6813007", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK441", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Vergleichsangebot", "notizen": "Detaillierte Beschreibung der Vergleichsangebot-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK442", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA441", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}]}, {"id": "I045", "name": "Birgit Binder", "adresse": "Lärchenweg 55, 48640 Mainz", "telefonnummer": "+49 839 2389593", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK451", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK452", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Preisanfrage", "notizen": "Detaillierte Beschreibung der Preisanfrage-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA451", "titel": "Probestellung anbieten", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Probestellung anbieten", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I046", "name": "<PERSON>", "adresse": "Zedernweg 2, 52755 Rostock", "telefonnummer": "+49 633 6999576", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK461", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK462", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via Garantiefall."}, {"id": "IK463", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Me<PERSON>besuch", "notizen": "Detaillierte Beschreibung der Messebesuch-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA461", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}, {"id": "IA462", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-22T09:00:00", "erledigt": false}]}, {"id": "I047", "name": "<PERSON>", "adresse": "Platanenhof 36, 22342 Aachen", "telefonnummer": "+49 265 9572036", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK471", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Erstberatung", "notizen": "Detaillierte Beschreibung der Erstberatung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA471", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-11T09:00:00", "erledigt": false}]}, {"id": "I048", "name": "<PERSON>", "adresse": "Buchenstraße 17, 24303 L<PERSON>beck", "telefonnummer": "+49 703 8023227", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK481", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}, {"id": "IK482", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Produktinteresse", "notizen": "Detaillierte Beschreibung der Produktinteresse-Kommunikation mit dem Interessenten via Sonstiges."}, {"id": "IK483", "datum": "2025-06-10T21:43:29", "typ": "Sonstiges", "beschreibung": "Probebestellung", "notizen": "Detaillierte Beschreibung der Probebestellung-Kommunikation mit dem Interessenten via Sonstiges."}], "aktionen": [{"id": "IA481", "titel": "Einladung zur Messe", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Einladung zur Messe", "faelligkeitsDatumZeit": "2025-06-12T09:00:00", "erledigt": false}]}, {"id": "I049", "name": "<PERSON>", "adresse": "Buchenstraße 36, 31748 Freiburg", "telefonnummer": "+49 105 4096118", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK491", "datum": "2025-06-10T21:43:29", "typ": "E-Mail", "beschreibung": "Empfehlung", "notizen": "Detaillierte Beschreibung der Empfehlung-Kommunikation mit dem Interessenten via E-Mail."}, {"id": "IK492", "datum": "2025-06-10T21:43:29", "typ": "<PERSON><PERSON><PERSON><PERSON>", "beschreibung": "Prospektanforderung", "notizen": "Detaillierte Beschreibung der Prospektanforderung-Kommunikation mit dem Interessenten via Garantiefall."}], "aktionen": [{"id": "IA491", "titel": "<PERSON>rstgespr<PERSON><PERSON>", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Erstgespräch", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}]}, {"id": "I050", "name": "<PERSON>", "adresse": "Weidenweg 39, 74380 Chemnitz", "telefonnummer": "+49 196 1641151", "email": "<EMAIL>", "kategorie": "INTERESSENT", "kommunikationen": [{"id": "IK501", "datum": "2025-06-10T21:43:29", "typ": "Telefon", "beschreibung": "Webseitenanfrage", "notizen": "Detaillierte Beschreibung der Webseitenanfrage-Kommunikation mit dem Interessenten via Telefon."}], "aktionen": [{"id": "IA501", "titel": "Nachfassen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Nachfassen", "faelligkeitsDatumZeit": "2025-06-20T09:00:00", "erledigt": false}, {"id": "IA502", "titel": "Ang<PERSON><PERSON> erstellen", "beschreibung": "Detaillierte Beschreibung der Interessenten-Aktion: Angebot erstellen", "faelligkeitsDatumZeit": "2025-06-15T09:00:00", "erledigt": false}]}, {"id": "K999", "name": "Test Heute", "adresse": "Teststraße 1, 12345 Berlin", "telefonnummer": "+49 123 456789", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [], "aktionen": [{"id": "AKT-H1", "titel": "Heute fällige Aktion 1", "beschreibung": "Diese Aktion ist heute fällig und muss erledigt werden.", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}, {"id": "AKT-H2", "titel": "Heute fällige Aktion 2", "beschreibung": "Diese Aktion ist heute fällig und muss erledigt werden.", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}, {"id": "AKT-H3", "titel": "Heute fällige Aktion 3", "beschreibung": "Diese Aktion ist heute fällig und muss erledigt werden.", "faelligkeitsDatumZeit": "2025-06-10T09:00:00", "erledigt": false}]}, {"id": "K998", "name": "Test Überfällig", "adresse": "Teststraße 2, 12345 Berlin", "telefonnummer": "+49 987 654321", "email": "<EMAIL>", "kategorie": "BESTANDSKUNDE", "kommunikationen": [], "aktionen": [{"id": "AKT-U1", "titel": "Überfällige Aktion 1", "beschreibung": "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.", "faelligkeitsDatumZeit": "2025-06-09T09:00:00", "erledigt": false}, {"id": "AKT-U2", "titel": "Überfällige Aktion 2", "beschreibung": "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.", "faelligkeitsDatumZeit": "2025-06-08T09:00:00", "erledigt": false}, {"id": "AKT-U3", "titel": "Überfällige Aktion 3", "beschreibung": "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.", "faelligkeitsDatumZeit": "2025-06-07T09:00:00", "erledigt": false}, {"id": "AKT-U4", "titel": "Überfällige Aktion 4", "beschreibung": "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.", "faelligkeitsDatumZeit": "2025-06-06T09:00:00", "erledigt": false}, {"id": "AKT-U5", "titel": "Überfällige Aktion 5", "beschreibung": "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.", "faelligkeitsDatumZeit": "2025-06-05T09:00:00", "erledigt": false}]}]}