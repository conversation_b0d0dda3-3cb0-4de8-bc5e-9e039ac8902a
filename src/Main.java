import gui.CrmGui;

import javax.swing.*;
import javax.swing.UIManager.LookAndFeelInfo;
import java.awt.*;

/**
 * Hauptklasse zum Starten des CRM-Systems.
 */
public class Main {
    public static void main(String[] args) {
        // Setze ein modernes Look and Feel
        setModernLookAndFeel();

        // Verwende SwingUtilities.invokeLater für Thread-Sicherheit bei Swing-Anwendungen
        SwingUtilities.invokeLater(() -> {
            // Erstelle und starte die GUI
            CrmGui gui = new CrmGui();
            // Hinweis: Die GUI wird in der CrmGui-Klasse sichtbar gemacht
        });
    }

    /**
     * Setzt ein modernes Look and Feel für die Anwendung.
     * Versucht zuerst Nimbus zu verwenden, fällt auf das System-Look-and-Feel zurück,
     * wenn Nimbus nicht verfügbar ist.
     */
    private static void setModernLookAndFeel() {
        try {
            // Versuche Nimbus Look and Feel zu verwenden
            for (LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());

                    // Anpassen der Nimbus-Farben für ein moderneres Aussehen
                    UIManager.put("nimbusBase", new Color(45, 125, 210));
                    UIManager.put("nimbusBlueGrey", new Color(240, 240, 240));
                    UIManager.put("control", new Color(250, 250, 250));

                    // Anpassen der Schriftart
                    Font defaultFont = new Font("Segoe UI", Font.PLAIN, 12);
                    UIManager.put("Button.font", defaultFont);
                    UIManager.put("Label.font", defaultFont);
                    UIManager.put("TextField.font", defaultFont);
                    UIManager.put("TextArea.font", defaultFont);
                    UIManager.put("ComboBox.font", defaultFont);
                    UIManager.put("List.font", defaultFont);
                    UIManager.put("Table.font", defaultFont);
                    UIManager.put("TableHeader.font", defaultFont.deriveFont(Font.BOLD));
                    UIManager.put("TabbedPane.font", defaultFont);
                    UIManager.put("Menu.font", defaultFont);
                    UIManager.put("MenuItem.font", defaultFont);

                    break;
                }
            }
        } catch (Exception e) {
            // Wenn Nimbus nicht verfügbar ist, verwende das System-Look-and-Feel
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception ex) {
                System.err.println("Fehler beim Setzen des Look and Feel: " + ex.getMessage());
            }
        }
    }
}
