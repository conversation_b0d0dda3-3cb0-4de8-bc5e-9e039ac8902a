package gui;

import model.NutzerDatenbank;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.io.File;

/**
 * Hauptpanel für die Statistik-Ansicht.
 */
public class StatistikPanel extends JPanel {
    private final NutzerDatenbank datenbank;
    private final DashboardPanel dashboardPanel;
    private final BerichtePanel berichtePanel;
    private final JTabbedPane tabbedPane;

    /**
     * Konstruktor für das Statistik-Panel.
     *
     * @param datenbank Die Kundendatenbank
     */
    public StatistikPanel(NutzerDatenbank datenbank) {
        this.datenbank = datenbank;

        // Setze Layout und Rahmen
        setLayout(new BorderLayout());
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // Erstelle die Komponenten
        dashboardPanel = new DashboardPanel(datenbank);
        berichtePanel = new BerichtePanel(datenbank);

        // Erstelle ein Tabbed-Panel für die verschiedenen Ansichten
        tabbedPane = new JTabbedPane();
        tabbedPane.addTab("Dashboard", dashboardPanel);
        tabbedPane.addTab("Berichte", berichtePanel);

        // Füge einen Tab-Wechsel-Listener hinzu
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            if (selectedIndex == 0) {
                // Aktualisiere das Dashboard, wenn es ausgewählt wird
                dashboardPanel.aktualisieren();
            }
        });

        // Füge das Tabbed-Panel zum Hauptpanel hinzu
        add(tabbedPane, BorderLayout.CENTER);
    }

    /**
     * Aktualisiert die Anzeige des Statistik-Panels.
     */
    public void aktualisieren() {
        // Aktualisiere das Dashboard
        dashboardPanel.aktualisieren();
    }

    /**
     * Exportiert den aktuellen Bericht.
     */
    public void exportiereBericht() {
        // Wenn der Berichte-Tab aktiv ist, delegiere die Aktion an das BerichtePanel
        if (tabbedPane.getSelectedIndex() == 1) {
            berichtePanel.exportiereBericht();
        } else {
            // Wechsle zum Berichte-Tab und exportiere dann
            tabbedPane.setSelectedIndex(1);
            berichtePanel.exportiereBericht();
        }
    }

    /**
     * Druckt den aktuellen Bericht.
     */
    public void druckeBericht() {
        // Wenn der Berichte-Tab aktiv ist, delegiere die Aktion an das BerichtePanel
        if (tabbedPane.getSelectedIndex() == 1) {
            berichtePanel.druckeBericht();
        } else {
            // Wechsle zum Berichte-Tab und drucke dann
            tabbedPane.setSelectedIndex(1);
            berichtePanel.druckeBericht();
        }
    }
}
