package gui;

import model.NutzerDatenbank;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Klasse für das Benachrichtigungs-Pop-up für anstehende und überfällige Aktionen.
 */
public class AktionenBenachrichtigung extends JDialog {

    /**
     * Konstruktor für das Benachrichtigungs-Pop-up.
     *
     * @param parent Das übergeordnete Frame
     * @param datenbank Die Kundendatenbank
     */
    public AktionenBenachrichtigung(Frame parent, NutzerDatenbank datenbank) {
        super(parent, "Aktionen-Benachrichtigung", true);
        
        // Hole die Anzahl der heutigen und überfälligen Aktionen
        int anzahlHeuteFaellig = datenbank.getAnzahlHeuteFaelligeAktionen();
        int anzahlUeberfaellig = datenbank.getAnzahlUeberfaelligeAktionen();
        
        // Wenn keine Aktionen vorhanden sind, zeige keinen Dialog an
        if (anzahlHeuteFaellig == 0 && anzahlUeberfaellig == 0) {
            dispose();
            return;
        }
        
        // Erstelle das Panel für die Benachrichtigung
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // Erstelle das Label mit der Benachrichtigung
        JLabel messageLabel = new JLabel();
        messageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        messageLabel.setFont(new Font(messageLabel.getFont().getName(), Font.BOLD, 14));
        
        // Setze den Text und die Hintergrundfarbe basierend auf den Aktionen
        String message;
        Color backgroundColor;
        
        if (anzahlUeberfaellig > 0 && anzahlHeuteFaellig > 0) {
            message = String.format("Achtung! Es gibt %d überfällige und %d heute fällige Aktionen!", 
                    anzahlUeberfaellig, anzahlHeuteFaellig);
            backgroundColor = new Color(255, 100, 100); // Rot
        } else if (anzahlUeberfaellig > 0) {
            message = String.format("Achtung! Es gibt %d überfällige Aktionen!", anzahlUeberfaellig);
            backgroundColor = new Color(255, 100, 100); // Rot
        } else {
            message = String.format("Achtung! Heute sind %d Aktionen durchzuführen!", anzahlHeuteFaellig);
            backgroundColor = new Color(255, 200, 100); // Orange
        }
        
        messageLabel.setText("<html><div style='text-align: center;'>" + message + "</div></html>");
        panel.setBackground(backgroundColor);
        
        // Erstelle den OK-Button
        JButton okButton = new JButton("OK");
        okButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
        
        // Füge die Komponenten zum Panel hinzu
        panel.add(messageLabel, BorderLayout.CENTER);
        
        JPanel buttonPanel = new JPanel();
        buttonPanel.setBackground(backgroundColor);
        buttonPanel.add(okButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Füge das Panel zum Dialog hinzu
        getContentPane().add(panel);
        
        // Setze die Größe und Position des Dialogs
        setSize(400, 200);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }
    
    /**
     * Zeigt das Benachrichtigungs-Pop-up an, wenn anstehende oder überfällige Aktionen vorhanden sind.
     *
     * @param parent Das übergeordnete Frame
     * @param datenbank Die Kundendatenbank
     */
    public static void zeigeAktionenBenachrichtigung(Frame parent, NutzerDatenbank datenbank) {
        int anzahlHeuteFaellig = datenbank.getAnzahlHeuteFaelligeAktionen();
        int anzahlUeberfaellig = datenbank.getAnzahlUeberfaelligeAktionen();
        
        // Nur anzeigen, wenn es Aktionen gibt
        if (anzahlHeuteFaellig > 0 || anzahlUeberfaellig > 0) {
            SwingUtilities.invokeLater(() -> {
                AktionenBenachrichtigung dialog = new AktionenBenachrichtigung(parent, datenbank);
                dialog.setVisible(true);
            });
        }
    }
}
