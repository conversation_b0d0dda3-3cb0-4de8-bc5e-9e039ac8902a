package gui;

import model.Aktion;
import model.Kunde;
import model.NutzerDatenbank;
import model.NutzerDatenbank.AktionMitKunde;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.List;
import javax.swing.Timer;

/**
 * Panel zur Anzeige eines Kalenders mit Aktionen.
 */
public class KalenderPanel extends JPanel {
    private final NutzerDatenbank datenbank;
    private final JPanel kalenderPanel;
    private final JLabel monatLabel;
    private final DateTimeFormatter monatFormatter = DateTimeFormatter.ofPattern("MMMM yyyy");
    private final DateTimeFormatter tagFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    private YearMonth aktuellerMonat;
    private Map<LocalDate, List<AktionMitKunde>> aktionenNachDatum;
    private LocalDate ausgewaehlterTag;
    private JTextArea detailsArea; // Für die Anzeige im CrmGui-Detailbereich
    private AktionenPanel aktionenPanel; // Panel für anstehende Aktionen
    private AktionSelectionListener aktionSelectionListener; // Listener für Aktionsauswahl
    private JSplitPane kalenderSplitPane; // SplitPane für Kalender und Aktionen-Panel

    /**
     * Konstruktor für das Kalender-Panel.
     *
     * @param datenbank Die Kundendatenbank
     */
    public KalenderPanel(NutzerDatenbank datenbank) {
        this(datenbank, null, null);
    }

    /**
     * Konstruktor für das Kalender-Panel mit Detailbereich.
     *
     * @param datenbank Die Kundendatenbank
     * @param detailsArea Der Textbereich für die Anzeige der Aktionendetails (kann null sein)
     */
    public KalenderPanel(NutzerDatenbank datenbank, JTextArea detailsArea) {
        this(datenbank, detailsArea, null);
    }

    /**
     * Konstruktor für das Kalender-Panel mit Detailbereich und Aktionsauswahl-Listener.
     *
     * @param datenbank Die Kundendatenbank
     * @param detailsArea Der Textbereich für die Anzeige der Aktionendetails (kann null sein)
     * @param listener Der Listener für Aktionsauswahl (kann null sein)
     */
    public KalenderPanel(NutzerDatenbank datenbank, JTextArea detailsArea, AktionSelectionListener listener) {
        this.datenbank = datenbank;
        this.aktuellerMonat = YearMonth.now();
        this.aktionenNachDatum = new HashMap<>();
        this.detailsArea = detailsArea;
        this.aktionSelectionListener = listener;

        setLayout(new BorderLayout(10, 10));
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // Panel für die Monatsnavigation
        JPanel navigationPanel = new JPanel(new BorderLayout());
        JButton vorMonatButton = new JButton("<");
        vorMonatButton.setForeground(Color.WHITE);
        vorMonatButton.setBackground(new Color(70, 130, 180));
        vorMonatButton.setFocusPainted(false);
        vorMonatButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(70, 130, 180).darker(), 1, true),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));

        // Füge einen Hover-Effekt hinzu
        vorMonatButton.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                vorMonatButton.setBackground(new Color(70, 130, 180).darker());
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                vorMonatButton.setBackground(new Color(70, 130, 180));
            }
        });

        JButton naechsterMonatButton = new JButton(">");
        naechsterMonatButton.setForeground(Color.WHITE);
        naechsterMonatButton.setBackground(new Color(70, 130, 180));
        naechsterMonatButton.setFocusPainted(false);
        naechsterMonatButton.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(70, 130, 180).darker(), 1, true),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));

        // Füge einen Hover-Effekt hinzu
        naechsterMonatButton.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                naechsterMonatButton.setBackground(new Color(70, 130, 180).darker());
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                naechsterMonatButton.setBackground(new Color(70, 130, 180));
            }
        });
        monatLabel = new JLabel("", SwingConstants.CENTER);
        monatLabel.setFont(monatLabel.getFont().deriveFont(Font.BOLD, 16f));

        navigationPanel.add(vorMonatButton, BorderLayout.WEST);
        navigationPanel.add(monatLabel, BorderLayout.CENTER);
        navigationPanel.add(naechsterMonatButton, BorderLayout.EAST);

        // Panel für den Kalender
        kalenderPanel = new JPanel(new GridLayout(0, 7, 5, 5));

        // Wir brauchen kein separates Panel für die Aktionsdetails, da wir den Detailbereich der CrmGui verwenden

        // Erstelle ein Panel für den Kalender mit Scrolling
        JScrollPane kalenderScrollPane = new JScrollPane(kalenderPanel);

        // Erstelle das Aktionen-Panel
        aktionenPanel = new AktionenPanel(datenbank);

        // Füge einen Listener hinzu, um auf Doppelklick auf eine Aktion zu reagieren
        aktionenPanel.addAktionSelectionListener((datum, titel, kundenName) -> {
            // Wenn ein Datum ausgewählt wurde, zeige die Aktionen für diesen Tag an
            try {
                LocalDate aktionsDatum = LocalDate.parse(datum, DateTimeFormatter.ofPattern("dd.MM.yyyy"));
                ausgewaehlterTag = aktionsDatum;
                zeigeAktionenFuerTag(aktionsDatum);
                aktualisiereKalenderAuswahl(); // Nur die Auswahl aktualisieren, nicht den ganzen Kalender
            } catch (Exception e) {
                // Ignoriere Parsing-Fehler
            }
        });

        // Füge einen Listener hinzu, um auf Einzelklick auf eine Aktion zu reagieren
        aktionenPanel.addSingleAktionSelectionListener(aktionMitKunde -> {
            // Zeige die Details der ausgewählten Aktion im Detailbereich an
            if (detailsArea != null) {
                StringBuilder details = new StringBuilder();
                Aktion aktion = aktionMitKunde.getAktion();
                Kunde kunde = aktionMitKunde.getKunde();

                details.append("Ausgewählte Aktion:\n\n");
                details.append("Titel: ").append(aktion.getTitel()).append("\n");
                details.append("Fällig am: ").append(aktion.getFaelligkeitsDatum().format(tagFormatter)).append("\n");
                details.append("Kunde: ").append(kunde.getName()).append("\n");

                String status;
                if (aktionMitKunde.istUeberfaellig()) {
                    status = "Überfällig";
                    details.append("Status: ").append(status).append(" (ROT)\n");
                } else if (aktionMitKunde.istHeuteFaellig()) {
                    status = "Heute fällig";
                    details.append("Status: ").append(status).append(" (ORANGE)\n");
                } else if (aktion.isErledigt()) {
                    status = "Erledigt";
                    details.append("Status: ").append(status).append(" (GRÜN)\n");
                } else {
                    status = "Anstehend";
                    details.append("Status: ").append(status).append("\n");
                }

                details.append("Beschreibung: ").append(aktion.getBeschreibung()).append("\n");
                details.append("ID: ").append(aktion.getId()).append("\n");

                detailsArea.setText(details.toString());

                // Setze den ausgewählten Tag zurück, damit keine Datumsmarkierung mehr angezeigt wird
                ausgewaehlterTag = null;
                aktualisiereKalenderAuswahl();

                // Informiere den Listener über die Aktionsauswahl
                if (aktionSelectionListener != null) {
                    aktionSelectionListener.onAktionSelected(aktionMitKunde);
                }
            }
        });

        // Erstelle ein Split-Panel für Kalender und Aktionen-Panel
        JSplitPane splitPane = new JSplitPane(
            JSplitPane.VERTICAL_SPLIT,
            kalenderScrollPane,
            aktionenPanel
        );

        // Setze eine optimale Divider-Position für eine gute Darstellung
        // Der Kalender bekommt 60% des verfügbaren Platzes, das Aktionen-Panel 40%
        splitPane.setResizeWeight(0.6);
        splitPane.setDividerLocation(0.6);

        // Speichere das SplitPane als Instanzvariable, um später darauf zugreifen zu können
        this.kalenderSplitPane = splitPane;

        // Füge alles zum Hauptpanel hinzu
        add(navigationPanel, BorderLayout.NORTH);
        add(splitPane, BorderLayout.CENTER);

        // Füge Event-Listener hinzu
        vorMonatButton.addActionListener(e -> {
            aktuellerMonat = aktuellerMonat.minusMonths(1);
            aktualisiereKalender();
        });

        naechsterMonatButton.addActionListener(e -> {
            aktuellerMonat = aktuellerMonat.plusMonths(1);
            aktualisiereKalender();
        });

        // Initialisiere den Kalender
        aktualisiereKalender();
    }

    /**
     * Aktualisiert den Kalender und die Aktionen.
     */
    public void aktualisiereKalender() {
        // Aktualisiere den Monatsnamen
        monatLabel.setText(aktuellerMonat.format(monatFormatter));

        // Hole alle Aktionen
        List<AktionMitKunde> alleAktionen = datenbank.getAlleAktionen();

        // Gruppiere Aktionen nach Datum
        aktionenNachDatum.clear();
        for (AktionMitKunde aktion : alleAktionen) {
            LocalDate datum = aktion.getAktion().getFaelligkeitsDatum();
            if (!aktionenNachDatum.containsKey(datum)) {
                aktionenNachDatum.put(datum, new ArrayList<>());
            }
            aktionenNachDatum.get(datum).add(aktion);
        }

        // Leere das Kalender-Panel
        kalenderPanel.removeAll();

        // Füge Wochentagsüberschriften hinzu
        for (DayOfWeek tag : DayOfWeek.values()) {
            JLabel wochentagLabel = new JLabel(tag.getDisplayName(TextStyle.SHORT, Locale.GERMAN), SwingConstants.CENTER);
            wochentagLabel.setFont(wochentagLabel.getFont().deriveFont(Font.BOLD));
            kalenderPanel.add(wochentagLabel);
        }

        // Bestimme den ersten Tag des Monats
        LocalDate ersterTag = aktuellerMonat.atDay(1);
        int ersterWochentag = ersterTag.getDayOfWeek().getValue(); // 1 = Montag, 7 = Sonntag

        // Füge leere Zellen für die Tage vor dem ersten Tag des Monats hinzu
        for (int i = 1; i < ersterWochentag; i++) {
            kalenderPanel.add(new JLabel(""));
        }

        // Füge Zellen für jeden Tag des Monats hinzu
        for (int tag = 1; tag <= aktuellerMonat.lengthOfMonth(); tag++) {
            LocalDate datum = aktuellerMonat.atDay(tag);
            JPanel tagPanel = erstelleTagPanel(datum);
            kalenderPanel.add(tagPanel);
        }

        // Aktualisiere die UI
        kalenderPanel.revalidate();
        kalenderPanel.repaint();

        // Wenn ein Tag ausgewählt ist, zeige dessen Aktionen an
        if (ausgewaehlterTag != null) {
            zeigeAktionenFuerTag(ausgewaehlterTag);
        } else {
            // Standardmäßig den heutigen Tag auswählen, wenn er im aktuellen Monat ist
            LocalDate heute = LocalDate.now();
            if (heute.getYear() == aktuellerMonat.getYear() && heute.getMonth() == aktuellerMonat.getMonth()) {
                zeigeAktionenFuerTag(heute);
            }
        }
    }

    /**
     * Erstellt ein Panel für einen Tag im Kalender.
     *
     * @param datum Das Datum des Tages
     * @return Ein Panel, das den Tag repräsentiert
     */
    private JPanel erstelleTagPanel(LocalDate datum) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));

        // Tag-Nummer
        JLabel tagLabel = new JLabel(String.valueOf(datum.getDayOfMonth()), SwingConstants.CENTER);

        // Hintergrundfarbe für den heutigen Tag
        if (datum.equals(LocalDate.now())) {
            panel.setBackground(new Color(230, 230, 255)); // Hellblau
            tagLabel.setFont(tagLabel.getFont().deriveFont(Font.BOLD));
        }

        // Hintergrundfarbe für den ausgewählten Tag
        if (datum.equals(ausgewaehlterTag)) {
            panel.setBackground(new Color(200, 200, 255)); // Dunkleres Blau
            panel.setBorder(BorderFactory.createLineBorder(Color.BLUE, 2));
        }

        panel.add(tagLabel, BorderLayout.NORTH);

        // Füge Aktionen für diesen Tag hinzu
        if (aktionenNachDatum.containsKey(datum)) {
            List<AktionMitKunde> aktionenAmTag = aktionenNachDatum.get(datum);

            // Zeige die Anzahl der Aktionen an
            int anzahlAktionen = aktionenAmTag.size();
            int anzahlUeberfaellig = 0;
            int anzahlHeuteFaellig = 0;
            int anzahlErledigt = 0;

            for (AktionMitKunde aktion : aktionenAmTag) {
                if (aktion.istUeberfaellig()) {
                    anzahlUeberfaellig++;
                } else if (aktion.istHeuteFaellig()) {
                    anzahlHeuteFaellig++;
                } else if (aktion.getAktion().isErledigt()) {
                    anzahlErledigt++;
                }
            }

            JPanel aktionenPanel = new JPanel(new GridLayout(0, 1, 2, 2));
            aktionenPanel.setOpaque(false);

            if (anzahlUeberfaellig > 0) {
                JLabel ueberfaelligLabel = new JLabel(anzahlUeberfaellig + " überfällig", SwingConstants.CENTER);
                ueberfaelligLabel.setForeground(Color.RED);
                ueberfaelligLabel.setFont(ueberfaelligLabel.getFont().deriveFont(Font.BOLD, 10f));
                aktionenPanel.add(ueberfaelligLabel);
            }

            if (anzahlHeuteFaellig > 0) {
                JLabel heuteFaelligLabel = new JLabel(anzahlHeuteFaellig + " heute", SwingConstants.CENTER);
                heuteFaelligLabel.setForeground(new Color(255, 140, 0)); // Orange
                heuteFaelligLabel.setFont(heuteFaelligLabel.getFont().deriveFont(Font.BOLD, 10f));
                aktionenPanel.add(heuteFaelligLabel);
            }

            int anzahlAnstehend = anzahlAktionen - anzahlUeberfaellig - anzahlHeuteFaellig - anzahlErledigt;
            if (anzahlAnstehend > 0) {
                JLabel anstehendLabel = new JLabel(anzahlAnstehend + " anstehend", SwingConstants.CENTER);
                anstehendLabel.setFont(anstehendLabel.getFont().deriveFont(10f));
                aktionenPanel.add(anstehendLabel);
            }

            if (anzahlErledigt > 0) {
                JLabel erledigtLabel = new JLabel(anzahlErledigt + " erledigt", SwingConstants.CENTER);
                erledigtLabel.setForeground(new Color(0, 128, 0)); // Grün
                erledigtLabel.setFont(erledigtLabel.getFont().deriveFont(10f));
                aktionenPanel.add(erledigtLabel);
            }

            panel.add(aktionenPanel, BorderLayout.CENTER);
        }

        // Füge einen Klick-Listener hinzu
        panel.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                ausgewaehlterTag = datum;
                zeigeAktionenFuerTag(datum);
                // Wichtig: Nicht die gesamte Kalenderansicht aktualisieren, sondern nur die Hervorhebung
                aktualisiereKalenderAuswahl();

                // Deselektiere alle Aktionen in der Aktionen-Tabelle
                aktionenPanel.clearSelection();

                // Aktualisiere die Aktionen-Tabelle, um nur Aktionen für diesen Tag anzuzeigen
                aktionenPanel.aktualisiereAktionen(datum);
            }
        });

        return panel;
    }

    /**
     * Zeigt die Aktionen für einen bestimmten Tag an.
     *
     * @param datum Das Datum, für das die Aktionen angezeigt werden sollen
     */
    private void zeigeAktionenFuerTag(LocalDate datum) {
        // Erstelle einen StringBuilder für die Detailansicht
        StringBuilder details = new StringBuilder();
        details.append("Aktionen am ").append(datum.format(tagFormatter)).append(":\n\n");

        // Füge Aktionen für diesen Tag hinzu
        boolean aktionenVorhanden = false;

        if (aktionenNachDatum.containsKey(datum)) {
            List<AktionMitKunde> aktionenAmTag = aktionenNachDatum.get(datum);

            for (AktionMitKunde aktionMitKunde : aktionenAmTag) {
                aktionenVorhanden = true;
                Aktion aktion = aktionMitKunde.getAktion();
                Kunde kunde = aktionMitKunde.getKunde();

                String titel = aktion.getTitel();
                String kundenName = kunde.getName();
                String id = aktion.getId();

                String status;
                if (aktionMitKunde.istUeberfaellig()) {
                    status = "Überfällig";
                    details.append("[ROT] "); // Visuelle Markierung für überfällige Aktionen
                } else if (aktionMitKunde.istHeuteFaellig()) {
                    status = "Heute fällig";
                    details.append("[ORANGE] "); // Visuelle Markierung für heute fällige Aktionen
                } else if (aktion.isErledigt()) {
                    status = "Erledigt";
                    details.append("[GRÜN] "); // Visuelle Markierung für erledigte Aktionen
                } else {
                    status = "Anstehend";
                }

                // Füge die Aktion zum Detailtext hinzu
                details.append("Titel: ").append(titel).append("\n");
                details.append("Kunde: ").append(kundenName).append("\n");
                details.append("Status: ").append(status).append("\n");
                details.append("ID: ").append(id).append("\n");
                details.append("Beschreibung: ").append(aktion.getBeschreibung()).append("\n\n");
            }
        }

        // Wenn keine Aktionen vorhanden sind, zeige eine Meldung
        if (!aktionenVorhanden) {
            details.append("Keine Aktionen für diesen Tag.");
        }

        // Aktualisiere den Detailbereich in CrmGui, falls vorhanden
        if (detailsArea != null) {
            detailsArea.setText(details.toString());
        }
    }

    /**
     * Aktualisiert nur die Hervorhebung des ausgewählten Tages im Kalender,
     * ohne den gesamten Kalender neu aufzubauen.
     */
    private void aktualisiereKalenderAuswahl() {
        // Durchlaufe alle Komponenten im Kalender-Panel
        for (Component comp : kalenderPanel.getComponents()) {
            if (comp instanceof JPanel) {
                JPanel tagPanel = (JPanel) comp;

                // Prüfe, ob dieses Panel einen MouseListener hat (also ein Tag-Panel ist)
                if (tagPanel.getMouseListeners().length > 0) {
                    // Hole das Datum aus dem ersten Label im Panel
                    Component[] components = tagPanel.getComponents();
                    if (components.length > 0 && components[0] instanceof JLabel) {
                        JLabel tagLabel = (JLabel) components[0];
                        try {
                            int tag = Integer.parseInt(tagLabel.getText());
                            LocalDate panelDatum = aktuellerMonat.atDay(tag);

                            // Setze die Hintergrundfarbe entsprechend
                            if (panelDatum.equals(LocalDate.now())) {
                                tagPanel.setBackground(new Color(230, 230, 255)); // Hellblau für heute
                            } else {
                                tagPanel.setBackground(UIManager.getColor("Panel.background")); // Standard
                            }

                            // Hervorhebung für den ausgewählten Tag
                            if (panelDatum.equals(ausgewaehlterTag)) {
                                tagPanel.setBackground(new Color(200, 200, 255)); // Dunkleres Blau
                                tagPanel.setBorder(BorderFactory.createLineBorder(Color.BLUE, 2));
                            } else {
                                tagPanel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
                            }
                        } catch (NumberFormatException e) {
                            // Ignoriere Parsing-Fehler (z.B. für Wochentagslabels)
                        }
                    }
                }
            }
        }

        // Aktualisiere die UI
        kalenderPanel.revalidate();
        kalenderPanel.repaint();
    }

    /**
     * Aktualisiert die Anzeige des Kalenders und der Aktionen.
     */
    public void aktualisieren() {
        aktualisiereKalender();
        aktionenPanel.aktualisiereAktionen();
    }

    /**
     * Optimiert die Divider-Position des SplitPanes für eine gute Darstellung.
     * Diese Methode sollte aufgerufen werden, wenn der Kalender-Tab angezeigt wird.
     */
    public void optimiereDividerPosition() {
        if (kalenderSplitPane != null) {
            // Setze die Divider-Position auf 60% der verfügbaren Höhe
            // Dies gibt dem Kalender ausreichend Platz, während das Aktionen-Panel gut sichtbar bleibt
            SwingUtilities.invokeLater(() -> {
                int height = kalenderSplitPane.getHeight();
                if (height > 0) {
                    kalenderSplitPane.setDividerLocation(0.6);
                } else {
                    // Falls die Höhe noch nicht verfügbar ist, versuche es später erneut
                    Timer timer = new Timer(100, e -> {
                        int newHeight = kalenderSplitPane.getHeight();
                        if (newHeight > 0) {
                            kalenderSplitPane.setDividerLocation(0.6);
                            ((Timer) e.getSource()).stop();
                        }
                    });
                    timer.setRepeats(true);
                    timer.start();
                }
            });
        }
    }

    /**
     * Leert die Anzeige der Aktionen und setzt die Auswahl zurück.
     * Diese Methode sollte aufgerufen werden, wenn zum Kalender-Tab gewechselt wird.
     */
    public void leereAktionenAnzeige() {
        // Setze den ausgewählten Tag zurück
        ausgewaehlterTag = null;

        // Leere die Aktionen-Tabelle
        aktionenPanel.leereTabelle();

        // Leere den Detailbereich, falls vorhanden
        if (detailsArea != null) {
            detailsArea.setText("Wählen Sie einen Tag im Kalender, um die Aktionen anzuzeigen.");
        }
    }

    /**
     * Gibt das AktionenPanel zurück.
     *
     * @return Das AktionenPanel
     */
    public AktionenPanel getAktionenPanel() {
        return aktionenPanel;
    }

    /**
     * Fügt einen Listener hinzu, der aufgerufen wird, wenn eine Aktion ausgewählt wird.
     *
     * @param listener Der Listener
     */
    public void addAktionSelectionListener(AktionSelectionListener listener) {
        this.aktionSelectionListener = listener;
    }

    /**
     * Interface für den Aktionsauswahl-Listener.
     */
    public interface AktionSelectionListener {
        void onAktionSelected(AktionMitKunde aktionMitKunde);
    }
}
