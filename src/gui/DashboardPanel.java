package gui;

import model.Kundenkategorie;
import model.NutzerDatenbank;
import model.StatistikDaten;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.Map;

/**
 * Panel zur Anzeige eines Dashboards mit Kennzahlen.
 */
public class DashboardPanel extends JPanel {
    private final NutzerDatenbank datenbank;
    private final StatistikDaten statistikDaten;
    
    private StatistikKartePanel kundenKarte;
    private StatistikKartePanel bestandskundenKarte;
    private StatistikKartePanel interessentenKarte;
    private StatistikKartePanel konversionsrateKarte;
    
    private StatistikKartePanel aktionenKarte;
    private StatistikKartePanel ueberfaelligeKarte;
    private StatistikKartePanel heuteFaelligeKarte;
    private StatistikKartePanel erledigteKarte;
    
    private StatistikKartePanel aktivitaetKarte;
    private StatistikKartePanel bearbeitungszeitKarte;

    /**
     * Konstruktor für das Dashboard-Panel.
     *
     * @param datenbank Die Kundendatenbank
     */
    public DashboardPanel(NutzerDatenbank datenbank) {
        this.datenbank = datenbank;
        this.statistikDaten = new StatistikDaten(datenbank);
        
        // Setze Layout und Rahmen
        setLayout(new BorderLayout());
        setBorder(new EmptyBorder(10, 10, 10, 10));
        
        // Erstelle die Komponenten
        initComponents();
        
        // Aktualisiere die Anzeige
        aktualisieren();
    }

    /**
     * Initialisiert die Komponenten des Dashboards.
     */
    private void initComponents() {
        // Erstelle ein Panel für die Karten mit GridLayout
        JPanel kartenPanel = new JPanel(new GridLayout(0, 3, 10, 10));
        kartenPanel.setBackground(new Color(240, 240, 240));
        
        // Erstelle die Karten für Kundendaten
        kundenKarte = new StatistikKartePanel(
            "Kunden gesamt",
            0,
            "Anzahl aller Kunden",
            new Color(230, 240, 255),
            new Color(70, 130, 180)
        );
        
        bestandskundenKarte = new StatistikKartePanel(
            "Bestandskunden",
            0,
            "Anzahl der Bestandskunden",
            new Color(230, 255, 230),
            new Color(60, 179, 113)
        );
        
        interessentenKarte = new StatistikKartePanel(
            "Interessenten",
            0,
            "Anzahl der Interessenten",
            new Color(255, 240, 230),
            new Color(255, 140, 0)
        );
        
        konversionsrateKarte = new StatistikKartePanel(
            "Konversionsrate",
            0.0,
            "Bestandskunden / Gesamtkunden",
            new Color(255, 230, 230),
            new Color(220, 20, 60)
        );
        
        // Erstelle die Karten für Aktionsdaten
        aktionenKarte = new StatistikKartePanel(
            "Aktionen gesamt",
            0,
            "Anzahl aller Aktionen",
            new Color(230, 240, 255),
            new Color(70, 130, 180)
        );
        
        ueberfaelligeKarte = new StatistikKartePanel(
            "Überfällige Aktionen",
            0,
            "Anzahl der überfälligen Aktionen",
            new Color(255, 230, 230),
            new Color(220, 20, 60)
        );
        
        heuteFaelligeKarte = new StatistikKartePanel(
            "Heute fällige Aktionen",
            0,
            "Anzahl der heute fälligen Aktionen",
            new Color(255, 240, 230),
            new Color(255, 140, 0)
        );
        
        erledigteKarte = new StatistikKartePanel(
            "Erledigte Aktionen",
            0,
            "Anzahl der erledigten Aktionen",
            new Color(230, 255, 230),
            new Color(60, 179, 113)
        );
        
        // Erstelle die Karten für weitere Kennzahlen
        aktivitaetKarte = new StatistikKartePanel(
            "Durchschnittliche Aktivität",
            0.0,
            "Aktionen + Kommunikationen pro Kunde",
            new Color(230, 240, 255),
            new Color(70, 130, 180)
        );
        
        bearbeitungszeitKarte = new StatistikKartePanel(
            "Bearbeitungszeit",
            0.0,
            "Durchschnittliche Tage bis Erledigung",
            new Color(230, 255, 230),
            new Color(60, 179, 113)
        );
        
        // Füge die Karten zum Panel hinzu
        kartenPanel.add(kundenKarte);
        kartenPanel.add(bestandskundenKarte);
        kartenPanel.add(interessentenKarte);
        kartenPanel.add(konversionsrateKarte);
        kartenPanel.add(aktionenKarte);
        kartenPanel.add(ueberfaelligeKarte);
        kartenPanel.add(heuteFaelligeKarte);
        kartenPanel.add(erledigteKarte);
        kartenPanel.add(aktivitaetKarte);
        kartenPanel.add(bearbeitungszeitKarte);
        
        // Erstelle ein Scroll-Panel für die Karten
        JScrollPane scrollPane = new JScrollPane(kartenPanel);
        scrollPane.setBorder(null);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        
        // Erstelle ein Panel für die Überschrift
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(new EmptyBorder(0, 0, 10, 0));
        
        JLabel headerLabel = new JLabel("CRM Dashboard");
        headerLabel.setFont(new Font("Arial", Font.BOLD, 24));
        headerLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        JButton aktualisierenButton = new JButton("Aktualisieren");
        aktualisierenButton.addActionListener(e -> aktualisieren());
        
        headerPanel.add(headerLabel, BorderLayout.CENTER);
        headerPanel.add(aktualisierenButton, BorderLayout.EAST);
        
        // Füge die Komponenten zum Hauptpanel hinzu
        add(headerPanel, BorderLayout.NORTH);
        add(scrollPane, BorderLayout.CENTER);
    }

    /**
     * Aktualisiert die Anzeige des Dashboards mit aktuellen Daten.
     */
    public void aktualisieren() {
        // Aktualisiere die Kundendaten
        int anzahlKunden = datenbank.getAnzahlKunden();
        kundenKarte.setWert(anzahlKunden);
        
        Map<Kundenkategorie, Integer> kundenNachKategorie = statistikDaten.getKundenNachKategorie();
        int anzahlBestandskunden = kundenNachKategorie.getOrDefault(Kundenkategorie.BESTANDSKUNDE, 0);
        int anzahlInteressenten = kundenNachKategorie.getOrDefault(Kundenkategorie.INTERESSENT, 0);
        
        bestandskundenKarte.setWert(anzahlBestandskunden);
        interessentenKarte.setWert(anzahlInteressenten);
        
        double konversionsrate = statistikDaten.getKonversionsrate();
        if (konversionsrate >= 0) {
            konversionsrateKarte.setWert(konversionsrate);
        } else {
            konversionsrateKarte.setWert("N/A");
        }
        
        // Aktualisiere die Aktionsdaten
        Map<String, Integer> aktionenNachStatus = statistikDaten.getAktionenNachStatus();
        int anzahlAktionen = aktionenNachStatus.values().stream().mapToInt(Integer::intValue).sum();
        int anzahlUeberfaellig = aktionenNachStatus.getOrDefault("Überfällig", 0);
        int anzahlHeuteFaellig = aktionenNachStatus.getOrDefault("Heute fällig", 0);
        int anzahlErledigt = aktionenNachStatus.getOrDefault("Erledigt", 0);
        
        aktionenKarte.setWert(anzahlAktionen);
        ueberfaelligeKarte.setWert(anzahlUeberfaellig);
        heuteFaelligeKarte.setWert(anzahlHeuteFaellig);
        erledigteKarte.setWert(anzahlErledigt);
        
        // Aktualisiere die weiteren Kennzahlen
        double durchschnittlicheAktivitaet = statistikDaten.getDurchschnittlicheAktivitaet();
        if (durchschnittlicheAktivitaet >= 0) {
            aktivitaetKarte.setWert(String.format("%.1f", durchschnittlicheAktivitaet));
        } else {
            aktivitaetKarte.setWert("N/A");
        }
        
        double durchschnittlicheBearbeitungszeit = statistikDaten.getDurchschnittlicheBearbeitungszeit();
        if (durchschnittlicheBearbeitungszeit >= 0) {
            bearbeitungszeitKarte.setWert(String.format("%.1f Tage", durchschnittlicheBearbeitungszeit));
        } else {
            bearbeitungszeitKarte.setWert("N/A");
        }
    }
}
