package gui;

import model.Aktion;
import model.Kunde;
import model.NutzerDatenbank;
import model.NutzerDatenbank.AktionMitKunde;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Panel zur Anzeige anstehender Aktionen.
 */
public class AktionenPanel extends JPanel {
    private final NutzerDatenbank datenbank;
    private final JTable aktionenTable;
    private final DefaultTableModel tableModel;
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    private Map<Integer, AktionMitKunde> aktionenMap = new HashMap<>(); // Speichert Aktionen nach Tabellenzeile
    private AktionSelectionListener aktionSelectionListener;
    private SingleAktionSelectionListener singleAktionSelectionListener;

    /**
     * Konstruktor für das Aktionen-Panel.
     *
     * @param datenbank Die Kundendatenbank
     */
    public AktionenPanel(NutzerDatenbank datenbank) {
        this.datenbank = datenbank;

        // Setze Layout und Rahmen
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createTitledBorder("Anstehende Aktionen"));

        // Erstelle Tabelle für Aktionen
        String[] columnNames = {"Fällig am", "Titel", "Kunde", "Status"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Keine Bearbeitung in der Tabelle erlauben
            }
        };

        aktionenTable = new JTable(tableModel);
        aktionenTable.setRowHeight(25);
        aktionenTable.getTableHeader().setReorderingAllowed(false);

        // Setze benutzerdefinierten Renderer für Farben
        aktionenTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(
                        table, value, isSelected, hasFocus, row, column);

                // Hole den Status aus der Tabelle
                String status = (String) table.getValueAt(row, 3);

                if ("Überfällig".equals(status)) {
                    c.setForeground(Color.RED);
                    setFont(getFont().deriveFont(Font.BOLD));
                } else if ("Heute fällig".equals(status)) {
                    c.setForeground(new Color(255, 140, 0)); // Orange
                    setFont(getFont().deriveFont(Font.BOLD));
                } else {
                    c.setForeground(table.getForeground());
                    setFont(getFont().deriveFont(Font.PLAIN));
                }

                return c;
            }
        });

        // Füge Tabelle zum Panel hinzu
        JScrollPane scrollPane = new JScrollPane(aktionenTable);
        scrollPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        add(scrollPane, BorderLayout.CENTER);

        // Füge Aktualisieren-Button hinzu
        JButton refreshButton = new JButton("Aktualisieren");
        refreshButton.addActionListener(e -> aktualisiereAktionen());

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(refreshButton);
        add(buttonPanel, BorderLayout.SOUTH);

        // Füge einen Listener für die Tabellenauswahl hinzu
        aktionenTable.getSelectionModel().addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = aktionenTable.getSelectedRow();
                    if (selectedRow >= 0 && tableModel.getRowCount() > 0 &&
                            !tableModel.getValueAt(selectedRow, 1).equals("Keine anstehenden Aktionen")) {
                        // Hole die ausgewählte Aktion
                        AktionMitKunde aktionMitKunde = aktionenMap.get(selectedRow);
                        if (aktionMitKunde != null && singleAktionSelectionListener != null) {
                            singleAktionSelectionListener.onSingleAktionSelected(aktionMitKunde);
                        }
                    }
                }
            }
        });

        // Initialisiere die Tabelle
        aktualisiereAktionen();
    }

    /**
     * Leert die Tabelle und zeigt eine Meldung an, dass keine Aktionen vorhanden sind.
     */
    public void leereTabelle() {
        // Lösche alle Zeilen
        tableModel.setRowCount(0);
        aktionenMap.clear();

        // Füge eine Zeile mit der Meldung hinzu
        tableModel.addRow(new Object[]{"", "Keine anstehenden Aktionen", "", ""});

        // Deselektiere alle Zeilen
        aktionenTable.clearSelection();
    }

    /**
     * Aktualisiert die Anzeige der anstehenden Aktionen.
     */
    public void aktualisiereAktionen() {
        aktualisiereAktionen(null);
    }

    /**
     * Aktualisiert die Anzeige der anstehenden Aktionen, gefiltert nach einem bestimmten Datum.
     *
     * @param filterDatum Das Datum, nach dem gefiltert werden soll, oder null für alle Aktionen
     */
    public void aktualisiereAktionen(LocalDate filterDatum) {
        // Lösche alle Zeilen
        tableModel.setRowCount(0);
        aktionenMap.clear();

        // Hole alle Aktionen
        List<AktionMitKunde> aktionen = datenbank.getAlleAktionen();

        // Füge Aktionen zur Tabelle hinzu
        int rowIndex = 0;
        boolean aktionenGefunden = false;

        for (AktionMitKunde aktionMitKunde : aktionen) {
            Aktion aktion = aktionMitKunde.getAktion();

            // Überspringe erledigte Aktionen
            if (aktion.isErledigt()) {
                continue;
            }

            // Filtere nach Datum, wenn ein Filterdatum angegeben wurde
            if (filterDatum != null && !aktion.getFaelligkeitsDatum().equals(filterDatum)) {
                continue;
            }

            aktionenGefunden = true;
            String datum = aktion.getFaelligkeitsDatum().format(dateFormatter);
            String zeit = aktion.getFaelligkeitsZeit().format(DateTimeFormatter.ofPattern("HH:mm"));
            String datumZeit = datum + " " + zeit;
            String titel = aktion.getTitel();
            String kundenName = aktionMitKunde.getKunde().getName();

            String status;
            if (aktionMitKunde.istUeberfaellig()) {
                status = "Überfällig";
            } else if (aktionMitKunde.istHeuteFaellig()) {
                status = "Heute fällig";
            } else {
                status = "Anstehend";
            }

            tableModel.addRow(new Object[]{datumZeit, titel, kundenName, status});
            aktionenMap.put(rowIndex++, aktionMitKunde);
        }

        // Wenn keine Aktionen gefunden wurden, zeige eine Meldung an
        if (!aktionenGefunden) {
            String meldung = filterDatum != null ?
                "Keine anstehenden Aktionen für den " + filterDatum.format(dateFormatter) :
                "Keine anstehenden Aktionen";
            tableModel.addRow(new Object[]{"", meldung, "", ""});
        }

        // Wenn keine Aktionen vorhanden sind, zeige eine Meldung
        if (tableModel.getRowCount() == 0) {
            tableModel.addRow(new Object[]{"", "Keine anstehenden Aktionen", "", ""});
        }
    }

    /**
     * Fügt einen Doppelklick-Listener hinzu, der eine Aktion ausführt, wenn auf eine Zeile geklickt wird.
     *
     * @param listener Der auszuführende Listener
     */
    public void addAktionSelectionListener(AktionSelectionListener listener) {
        this.aktionSelectionListener = listener;
        aktionenTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                if (evt.getClickCount() == 2) {
                    int row = aktionenTable.getSelectedRow();
                    if (row >= 0 && tableModel.getRowCount() > 0 &&
                            !tableModel.getValueAt(row, 1).equals("Keine anstehenden Aktionen")) {
                        String datum = (String) tableModel.getValueAt(row, 0);
                        String titel = (String) tableModel.getValueAt(row, 1);
                        String kundenName = (String) tableModel.getValueAt(row, 2);

                        listener.onAktionSelected(datum, titel, kundenName);
                    }
                }
            }
        });
    }

    /**
     * Fügt einen Listener hinzu, der aufgerufen wird, wenn eine einzelne Aktion ausgewählt wird.
     *
     * @param listener Der auszuführende Listener
     */
    public void addSingleAktionSelectionListener(SingleAktionSelectionListener listener) {
        this.singleAktionSelectionListener = listener;
    }

    /**
     * Gibt die aktuell ausgewählte Aktion zurück.
     *
     * @return Die ausgewählte Aktion oder null, wenn keine ausgewählt ist
     */
    public AktionMitKunde getSelectedAktion() {
        int selectedRow = aktionenTable.getSelectedRow();
        if (selectedRow >= 0 && tableModel.getRowCount() > 0 &&
                !tableModel.getValueAt(selectedRow, 1).equals("Keine anstehenden Aktionen")) {
            return aktionenMap.get(selectedRow);
        }
        return null;
    }

    /**
     * Deselektiert alle Zeilen in der Aktionen-Tabelle.
     */
    public void clearSelection() {
        aktionenTable.clearSelection();
    }

    /**
     * Interface für den Aktions-Auswahl-Listener.
     */
    public interface AktionSelectionListener {
        void onAktionSelected(String datum, String titel, String kundenName);
    }

    /**
     * Interface für den Einzelaktions-Auswahl-Listener.
     */
    public interface SingleAktionSelectionListener {
        void onSingleAktionSelected(AktionMitKunde aktionMitKunde);
    }
}
