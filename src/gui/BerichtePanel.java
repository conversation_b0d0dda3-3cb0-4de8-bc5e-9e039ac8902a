package gui;

import model.Kunde;
import model.Kundenkategorie;
import model.NutzerDatenbank;
import model.StatistikDaten;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Panel zur Anzeige und Erstellung von Berichten.
 */
public class BerichtePanel extends JPanel {
    private final NutzerDatenbank datenbank;
    private final StatistikDaten statistikDaten;

    private JComboBox<String> berichtTypComboBox;
    private JPanel filterPanel;
    private JPanel berichtPanel;
    private JTable berichtTable;
    private DefaultTableModel tableModel;
    private JButton exportierenButton;
    private JButton druckenButton;

    /**
     * Konstruktor für das Berichte-Panel.
     *
     * @param datenbank Die Kundendatenbank
     */
    public BerichtePanel(NutzerDatenbank datenbank) {
        this.datenbank = datenbank;
        this.statistikDaten = new StatistikDaten(datenbank);

        // Setze Layout und Rahmen
        setLayout(new BorderLayout());
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // Erstelle die Komponenten
        initComponents();
    }

    /**
     * Initialisiert die Komponenten des Berichte-Panels.
     */
    private void initComponents() {
        // Erstelle das Header-Panel
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(new EmptyBorder(0, 0, 10, 0));

        JLabel headerLabel = new JLabel("Berichte");
        headerLabel.setFont(new Font("Arial", Font.BOLD, 24));

        // Erstelle die Berichtstyp-Auswahl
        String[] berichtTypen = {
            "Kundenübersicht",
            "Aktionsübersicht",
            "Kommunikationsübersicht",
            "Aktivitätsübersicht"
        };

        berichtTypComboBox = new JComboBox<>(berichtTypen);
        berichtTypComboBox.addActionListener(e -> erstelleBericht());

        headerPanel.add(headerLabel, BorderLayout.WEST);
        headerPanel.add(berichtTypComboBox, BorderLayout.EAST);

        // Erstelle das Filter-Panel
        filterPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        filterPanel.setBorder(BorderFactory.createTitledBorder("Filter"));

        // Erstelle das Bericht-Panel
        berichtPanel = new JPanel(new BorderLayout());
        berichtPanel.setBorder(BorderFactory.createTitledBorder("Bericht"));

        // Erstelle die Tabelle für den Bericht
        tableModel = new DefaultTableModel();
        berichtTable = new JTable(tableModel);
        berichtTable.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        berichtTable.getTableHeader().setReorderingAllowed(false);

        JScrollPane tableScrollPane = new JScrollPane(berichtTable);
        berichtPanel.add(tableScrollPane, BorderLayout.CENTER);

        // Erstelle das Button-Panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        exportierenButton = new JButton("Exportieren");
        exportierenButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                exportiereBericht();
            }
        });

        druckenButton = new JButton("Drucken");
        druckenButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                druckeBericht();
            }
        });

        buttonPanel.add(exportierenButton);
        buttonPanel.add(druckenButton);

        berichtPanel.add(buttonPanel, BorderLayout.SOUTH);

        // Füge die Komponenten zum Hauptpanel hinzu
        add(headerPanel, BorderLayout.NORTH);
        add(filterPanel, BorderLayout.CENTER);
        add(berichtPanel, BorderLayout.SOUTH);

        // Erstelle den ersten Bericht
        erstelleBericht();
    }

    /**
     * Erstellt einen Bericht basierend auf dem ausgewählten Berichtstyp.
     */
    private void erstelleBericht() {
        String berichtTyp = (String) berichtTypComboBox.getSelectedItem();

        if (berichtTyp == null) {
            return;
        }

        // Leere die Tabelle
        tableModel.setRowCount(0);
        tableModel.setColumnCount(0);

        // Aktualisiere das Filter-Panel
        filterPanel.removeAll();

        switch (berichtTyp) {
            case "Kundenübersicht":
                erstelleKundenBericht();
                break;
            case "Aktionsübersicht":
                erstelleAktionenBericht();
                break;
            case "Kommunikationsübersicht":
                erstelleKommunikationenBericht();
                break;
            case "Aktivitätsübersicht":
                erstelleAktivitaetsBericht();
                break;
        }

        // Aktualisiere die Anzeige
        filterPanel.revalidate();
        filterPanel.repaint();
    }

    /**
     * Erstellt einen Bericht über die Kunden.
     */
    private void erstelleKundenBericht() {
        // Füge Filter hinzu
        JComboBox<String> kategorieFilter = new JComboBox<>(new String[]{"Alle", "Bestandskunden", "Interessenten"});
        kategorieFilter.addActionListener(e -> {
            String auswahl = (String) kategorieFilter.getSelectedItem();
            List<Kunde> kunden;

            if ("Bestandskunden".equals(auswahl)) {
                kunden = datenbank.filterByKategorie(Kundenkategorie.BESTANDSKUNDE);
            } else if ("Interessenten".equals(auswahl)) {
                kunden = datenbank.filterByKategorie(Kundenkategorie.INTERESSENT);
            } else {
                kunden = datenbank.getAlleKunden();
            }

            aktualisiereKundenTabelle(kunden);
        });

        filterPanel.add(new JLabel("Kategorie:"));
        filterPanel.add(kategorieFilter);

        // Erstelle die Tabelle
        String[] columnNames = {"ID", "Name", "Kategorie", "E-Mail", "Telefon", "Aktionen", "Kommunikationen"};
        tableModel.setColumnIdentifiers(columnNames);

        // Fülle die Tabelle mit Daten
        aktualisiereKundenTabelle(datenbank.getAlleKunden());
    }

    /**
     * Aktualisiert die Tabelle mit Kundendaten.
     *
     * @param kunden Die anzuzeigenden Kunden
     */
    private void aktualisiereKundenTabelle(List<Kunde> kunden) {
        tableModel.setRowCount(0);

        for (Kunde kunde : kunden) {
            Object[] row = {
                kunde.getId(),
                kunde.getName(),
                kunde.getKategorie().getBezeichnung(),
                kunde.getEmail(),
                kunde.getTelefonnummer(),
                kunde.getAktionen().size(),
                kunde.getKommunikationen().size()
            };

            tableModel.addRow(row);
        }
    }

    /**
     * Erstellt einen Bericht über die Aktionen.
     */
    private void erstelleAktionenBericht() {
        // Füge Filter hinzu
        JComboBox<String> statusFilter = new JComboBox<>(new String[]{"Alle", "Überfällig", "Heute fällig", "Anstehend", "Erledigt"});
        statusFilter.addActionListener(e -> {
            String auswahl = (String) statusFilter.getSelectedItem();
            List<NutzerDatenbank.AktionMitKunde> aktionen = datenbank.getAlleAktionen();

            // Filtere die Aktionen nach Status
            if (!"Alle".equals(auswahl)) {
                aktualisiereAktionenTabelle(aktionen, auswahl);
            } else {
                aktualisiereAktionenTabelle(aktionen, null);
            }
        });

        filterPanel.add(new JLabel("Status:"));
        filterPanel.add(statusFilter);

        // Erstelle die Tabelle
        String[] columnNames = {"ID", "Titel", "Kunde", "Fällig am", "Status", "Erledigt"};
        tableModel.setColumnIdentifiers(columnNames);

        // Fülle die Tabelle mit Daten
        aktualisiereAktionenTabelle(datenbank.getAlleAktionen(), null);
    }

    /**
     * Aktualisiert die Tabelle mit Aktionsdaten.
     *
     * @param aktionen Die anzuzeigenden Aktionen
     * @param statusFilter Der optionale Statusfilter
     */
    private void aktualisiereAktionenTabelle(List<NutzerDatenbank.AktionMitKunde> aktionen, String statusFilter) {
        tableModel.setRowCount(0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");

        for (NutzerDatenbank.AktionMitKunde aktionMitKunde : aktionen) {
            String status;
            if (aktionMitKunde.getAktion().isErledigt()) {
                status = "Erledigt";
            } else if (aktionMitKunde.istUeberfaellig()) {
                status = "Überfällig";
            } else if (aktionMitKunde.istHeuteFaellig()) {
                status = "Heute fällig";
            } else {
                status = "Anstehend";
            }

            // Filtere nach Status, wenn ein Filter gesetzt ist
            if (statusFilter != null && !statusFilter.equals(status)) {
                continue;
            }

            Object[] row = {
                aktionMitKunde.getAktion().getId(),
                aktionMitKunde.getAktion().getTitel(),
                aktionMitKunde.getKunde().getName(),
                aktionMitKunde.getAktion().getFaelligkeitsDatum().format(formatter),
                status,
                aktionMitKunde.getAktion().isErledigt() ? "Ja" : "Nein"
            };

            tableModel.addRow(row);
        }
    }

    /**
     * Erstellt einen Bericht über die Kommunikationen.
     */
    private void erstelleKommunikationenBericht() {
        // Füge Filter hinzu
        JComboBox<String> typFilter = new JComboBox<>(new String[]{"Alle", "E-Mail", "Anruf", "Kundengespräch", "Garantiefall", "Sonstiges"});
        typFilter.addActionListener(e -> {
            String auswahl = (String) typFilter.getSelectedItem();

            // Filtere die Kommunikationen nach Typ
            if (!"Alle".equals(auswahl)) {
                aktualisiereKommunikationenTabelle(auswahl);
            } else {
                aktualisiereKommunikationenTabelle(null);
            }
        });

        filterPanel.add(new JLabel("Typ:"));
        filterPanel.add(typFilter);

        // Erstelle die Tabelle
        String[] columnNames = {"ID", "Kunde", "Typ", "Datum", "Beschreibung"};
        tableModel.setColumnIdentifiers(columnNames);

        // Fülle die Tabelle mit Daten
        aktualisiereKommunikationenTabelle(null);
    }

    /**
     * Aktualisiert die Tabelle mit Kommunikationsdaten.
     *
     * @param typFilter Der optionale Typfilter
     */
    private void aktualisiereKommunikationenTabelle(String typFilter) {
        tableModel.setRowCount(0);

        for (Kunde kunde : datenbank.getAlleKunden()) {
            for (model.Kommunikation kommunikation : kunde.getKommunikationen()) {
                // Filtere nach Typ, wenn ein Filter gesetzt ist
                if (typFilter != null && !typFilter.equals(kommunikation.getTyp())) {
                    continue;
                }

                Object[] row = {
                    kommunikation.getId(),
                    kunde.getName(),
                    kommunikation.getTyp(),
                    kommunikation.getFormattiertesDatum(),
                    kommunikation.getBeschreibung()
                };

                tableModel.addRow(row);
            }
        }
    }

    /**
     * Erstellt einen Bericht über die Aktivitäten der Kunden.
     */
    private void erstelleAktivitaetsBericht() {
        // Erstelle die Tabelle
        String[] columnNames = {"ID", "Kunde", "Kategorie", "Aktionen", "Kommunikationen", "Aktivitätswert"};
        tableModel.setColumnIdentifiers(columnNames);

        // Fülle die Tabelle mit Daten
        for (Kunde kunde : datenbank.getAlleKunden()) {
            int anzahlAktionen = kunde.getAktionen().size();
            int anzahlKommunikationen = kunde.getKommunikationen().size();
            int aktivitaetswert = anzahlAktionen + anzahlKommunikationen;

            Object[] row = {
                kunde.getId(),
                kunde.getName(),
                kunde.getKategorie().getBezeichnung(),
                anzahlAktionen,
                anzahlKommunikationen,
                aktivitaetswert
            };

            tableModel.addRow(row);
        }
    }

    /**
     * Exportiert den aktuellen Bericht als CSV-Datei.
     */
    public void exportiereBericht() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("Bericht exportieren");
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        fileChooser.setSelectedFile(new File("bericht.csv"));

        int userSelection = fileChooser.showSaveDialog(this);

        if (userSelection == JFileChooser.APPROVE_OPTION) {
            File fileToSave = fileChooser.getSelectedFile();

            try (FileWriter writer = new FileWriter(fileToSave)) {
                // Schreibe die Spaltenüberschriften
                for (int i = 0; i < tableModel.getColumnCount(); i++) {
                    writer.append(tableModel.getColumnName(i));
                    if (i < tableModel.getColumnCount() - 1) {
                        writer.append(",");
                    }
                }
                writer.append("\n");

                // Schreibe die Daten
                for (int i = 0; i < tableModel.getRowCount(); i++) {
                    for (int j = 0; j < tableModel.getColumnCount(); j++) {
                        writer.append(tableModel.getValueAt(i, j).toString());
                        if (j < tableModel.getColumnCount() - 1) {
                            writer.append(",");
                        }
                    }
                    writer.append("\n");
                }

                writer.flush();
                JOptionPane.showMessageDialog(this, "Bericht wurde erfolgreich exportiert.", "Export erfolgreich", JOptionPane.INFORMATION_MESSAGE);
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "Fehler beim Exportieren des Berichts: " + e.getMessage(), "Fehler", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * Druckt den aktuellen Bericht.
     */
    public void druckeBericht() {
        try {
            boolean complete = berichtTable.print();
            if (complete) {
                JOptionPane.showMessageDialog(this, "Druck erfolgreich abgeschlossen.", "Drucken", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this, "Druck wurde abgebrochen.", "Drucken", JOptionPane.WARNING_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Fehler beim Drucken: " + e.getMessage(), "Fehler", JOptionPane.ERROR_MESSAGE);
        }
    }
}
