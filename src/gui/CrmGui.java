package gui;

import model.*;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.event.ChangeListener;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.swing.SpinnerDateModel;

/**
 * Eine einfache GUI für das CRM-System.
 * Diese Klasse demonstriert, wie eine grundlegende Benutzeroberfläche zur Verwaltung von Kunden erstellt wird.
 */
public class CrmGui extends JFrame {
    private final NutzerDatenbank datenbank;
    private JList<String> kundenListe;
    private DefaultListModel<String> listModel;
    private JTabbedPane kundenTabs;
    private JList<String> bestandskundenListe;
    private JList<String> interessentenListe;
    private DefaultListModel<String> bestandskundenModel;
    private DefaultListModel<String> interessentenModel;
    private JTextField searchField;
    private JPanel searchPanel;
    private JTextArea detailsArea;
    private JButton addButton;
    private JButton editButton;
    private JButton deleteButton;
    private JButton addKommunikationButton;
    private JButton addAktionButton;
    private JButton markiereErledigtButton;
    private JButton loescheAktionButton;
    private KalenderPanel kalenderPanel;
    private JScrollPane detailsScrollPane;
    private JTable aktionenTable;
    private DefaultTableModel aktionenTableModel;
    private Kunde aktuellerKunde;
    private JPanel buttonPanel;
    private final String KUNDEN_BUTTONS = "kundenButtons";
    private final String KALENDER_BUTTONS = "kalenderButtons";
    private final String STATISTIK_BUTTONS = "statistikButtons";
    private NutzerDatenbank.AktionMitKunde ausgewaehlteAktion;
    private StatistikPanel statistikPanel;
    private JSplitPane horizontalSplitPane;

    /**
     * Konstruktor zum Erstellen der CRM-GUI.
     */
    public CrmGui() {
        super("CRM-System");
        this.datenbank = new NutzerDatenbank();

        // Füge einige Beispieldaten hinzu
        initializeSampleData();

        // Richte die GUI-Komponenten ein
        setupUI();

        // Setze Fenster-Eigenschaften
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);

        // Maximiere das Fenster beim Start
        setExtendedState(JFrame.MAXIMIZED_BOTH);

        // Zeige Benachrichtigung für anstehende und überfällige Aktionen
        setVisible(true); // Mache das Fenster zuerst sichtbar, damit der Dialog darüber erscheint
        AktionenBenachrichtigung.zeigeAktionenBenachrichtigung(this, datenbank);
    }

    /**
     * Initialisiert die Datenbank mit Beispieldaten.
     */
    private void initializeSampleData() {
        // Erstelle 100 Bestandskunden
        erstelleBeispielBestandskunden(100);

        // Erstelle 50 Interessenten
        erstelleBeispielInteressenten(50);

        // Erstelle einige heute fällige und überfällige Aktionen für Testzwecke
        erstelleTestAktionen();
    }

    /**
     * Erstellt eine bestimmte Anzahl von Beispiel-Bestandskunden mit zufälligen Daten.
     *
     * @param anzahl Die Anzahl der zu erstellenden Bestandskunden
     */
    private void erstelleBeispielBestandskunden(int anzahl) {
        String[] vornamen = {"Max", "Thomas", "Michael", "Andreas", "Stefan", "Christian", "Martin", "Daniel", "Markus", "Alexander",
                            "Julia", "Anna", "Lisa", "Sarah", "Laura", "Katharina", "Melanie", "Christina", "Maria", "Sandra"};

        String[] nachnamen = {"Müller", "Schmidt", "Schneider", "Fischer", "Weber", "Meyer", "Wagner", "Becker", "Schulz", "Hoffmann",
                            "Schäfer", "Koch", "Bauer", "Richter", "Klein", "Wolf", "Schröder", "Neumann", "Schwarz", "Zimmermann"};

        String[] staedte = {"Berlin", "Hamburg", "München", "Köln", "Frankfurt", "Stuttgart", "Düsseldorf", "Dortmund", "Essen", "Leipzig",
                          "Bremen", "Dresden", "Hannover", "Nürnberg", "Duisburg", "Bochum", "Wuppertal", "Bielefeld", "Bonn", "Münster"};

        String[] strassen = {"Hauptstraße", "Schulstraße", "Gartenstraße", "Bahnhofstraße", "Dorfstraße", "Bergstraße", "Parkstraße",
                           "Kirchstraße", "Waldstraße", "Wiesenweg", "Rosenweg", "Lindenallee", "Eichenweg", "Birkenstraße", "Tannenweg"};

        String[] kommunikationsTypen = {"E-Mail", "Telefon", "Kundengespräch", "Garantiefall", "Sonstiges"};

        String[] kommunikationsThemen = {"Erstkontakt", "Produktanfrage", "Beratungsgespräch", "Beschwerde", "Produktvorstellung",
                                      "Vertragsverhandlung", "Nachfrage", "Support", "Reklamation", "Feedback"};

        String[] aktionsTitel = {"Nachfassen", "Angebot erstellen", "Termin vereinbaren", "Produktpräsentation", "Vertrag zusenden",
                               "Rückruf", "Beschwerde bearbeiten", "Informationen zusenden", "Rabatt anbieten", "Kundenbindungsmaßnahme"};

        // Erstelle die angegebene Anzahl von Bestandskunden
        for (int i = 1; i <= anzahl; i++) {
            // Generiere zufällige Daten
            String vorname = vornamen[(int) (Math.random() * vornamen.length)];
            String nachname = nachnamen[(int) (Math.random() * nachnamen.length)];
            String stadt = staedte[(int) (Math.random() * staedte.length)];
            String strasse = strassen[(int) (Math.random() * strassen.length)];
            int hausnummer = 1 + (int) (Math.random() * 100);
            int plz = 10000 + (int) (Math.random() * 90000);

            // Erstelle den Kunden
            Kunde kunde = new Kunde(
                "K" + String.format("%03d", i + 3), // IDs beginnen bei K004, da wir bereits 3 Kunden haben
                vorname + " " + nachname,
                strasse + " " + hausnummer + ", " + plz + " " + stadt,
                "+49 " + (100 + (int) (Math.random() * 900)) + " " + (1000000 + (int) (Math.random() * 9000000)),
                vorname.toLowerCase() + "." + nachname.toLowerCase() + "@example.com",
                Kundenkategorie.BESTANDSKUNDE
            );

            // Füge 1-5 zufällige Kommunikationen hinzu
            int anzahlKommunikationen = 1 + (int) (Math.random() * 5);
            for (int j = 1; j <= anzahlKommunikationen; j++) {
                String typ = kommunikationsTypen[(int) (Math.random() * kommunikationsTypen.length)];
                String thema = kommunikationsThemen[(int) (Math.random() * kommunikationsThemen.length)];

                Kommunikation kommunikation = new Kommunikation(
                    "KOM" + String.format("%03d", (i * 10) + j),
                    typ,
                    thema,
                    "Detaillierte Beschreibung der " + thema + "-Kommunikation via " + typ + "."
                );
                kunde.addKommunikation(kommunikation);
            }

            // Füge 0-3 zufällige Aktionen hinzu (nicht für jeden Kunden)
            if (Math.random() < 0.7) { // 70% Wahrscheinlichkeit für Aktionen
                int anzahlAktionen = (int) (Math.random() * 3);
                for (int j = 1; j <= anzahlAktionen; j++) {
                    String titel = aktionsTitel[(int) (Math.random() * aktionsTitel.length)];

                    // Zufälliges Datum in den nächsten 30 Tagen
                    int tage = (int) (Math.random() * 30);

                    Aktion aktion = new Aktion(
                        "AKT" + String.format("%03d", (i * 10) + j),
                        titel,
                        "Detaillierte Beschreibung der Aktion: " + titel,
                        LocalDate.now().plusDays(tage)
                    );

                    // 20% Wahrscheinlichkeit, dass die Aktion bereits erledigt ist
                    if (Math.random() < 0.2) {
                        aktion.setErledigt(true);
                    }

                    kunde.addAktion(aktion);
                }
            }

            // Füge den Kunden zur Datenbank hinzu
            datenbank.addKunde(kunde);
        }
    }

    /**
     * Erstellt eine bestimmte Anzahl von Beispiel-Interessenten mit zufälligen Daten.
     *
     * @param anzahl Die Anzahl der zu erstellenden Interessenten
     */
    private void erstelleBeispielInteressenten(int anzahl) {
        String[] vornamen = {"Peter", "Frank", "Jürgen", "Klaus", "Wolfgang", "Matthias", "Oliver", "Sven", "Jan", "Bernd",
                            "Sabine", "Nicole", "Petra", "Susanne", "Claudia", "Andrea", "Birgit", "Stefanie", "Karin", "Monika"};

        String[] nachnamen = {"Huber", "Maier", "Gruber", "Schmid", "Winkler", "Hofer", "Pichler", "Steiner", "Moser", "Mayer",
                            "Berger", "Eder", "Fuchs", "Lang", "Baumgartner", "Auer", "Binder", "Wieser", "Ebner", "Reiter"};

        String[] staedte = {"Augsburg", "Aachen", "Braunschweig", "Chemnitz", "Freiburg", "Heidelberg", "Karlsruhe", "Mainz", "Potsdam", "Regensburg",
                          "Rostock", "Saarbrücken", "Trier", "Ulm", "Wolfsburg", "Würzburg", "Göttingen", "Kiel", "Lübeck", "Magdeburg"};

        String[] strassen = {"Ahornweg", "Buchenstraße", "Erlenweg", "Fichtenstraße", "Kastanienallee", "Kiefernweg", "Ulmenstraße",
                           "Eschenweg", "Pappelstraße", "Zedernweg", "Akazienstraße", "Lärchenweg", "Magnolienstraße", "Platanenhof", "Weidenweg"};

        String[] kommunikationsTypen = {"E-Mail", "Telefon", "Kundengespräch", "Garantiefall", "Sonstiges"};

        String[] kommunikationsThemen = {"Informationsanfrage", "Produktinteresse", "Preisanfrage", "Erstberatung", "Messebesuch",
                                      "Empfehlung", "Webseitenanfrage", "Prospektanforderung", "Probebestellung", "Vergleichsangebot"};

        String[] aktionsTitel = {"Erstgespräch", "Produktvorstellung", "Bedarfsanalyse", "Angebot erstellen", "Nachfassen",
                               "Produktmuster senden", "Einladung zur Messe", "Katalog zusenden", "Preisverhandlung", "Probestellung anbieten"};

        // Erstelle die angegebene Anzahl von Interessenten
        for (int i = 1; i <= anzahl; i++) {
            // Generiere zufällige Daten
            String vorname = vornamen[(int) (Math.random() * vornamen.length)];
            String nachname = nachnamen[(int) (Math.random() * nachnamen.length)];
            String stadt = staedte[(int) (Math.random() * staedte.length)];
            String strasse = strassen[(int) (Math.random() * strassen.length)];
            int hausnummer = 1 + (int) (Math.random() * 100);
            int plz = 10000 + (int) (Math.random() * 90000);

            // Erstelle den Interessenten
            Kunde interessent = new Kunde(
                "I" + String.format("%03d", i), // IDs beginnen bei I001 für Interessenten
                vorname + " " + nachname,
                strasse + " " + hausnummer + ", " + plz + " " + stadt,
                "+49 " + (100 + (int) (Math.random() * 900)) + " " + (1000000 + (int) (Math.random() * 9000000)),
                vorname.toLowerCase() + "." + nachname.toLowerCase() + "@example.com",
                Kundenkategorie.INTERESSENT
            );

            // Füge 1-3 zufällige Kommunikationen hinzu
            int anzahlKommunikationen = 1 + (int) (Math.random() * 3);
            for (int j = 1; j <= anzahlKommunikationen; j++) {
                String typ = kommunikationsTypen[(int) (Math.random() * kommunikationsTypen.length)];
                String thema = kommunikationsThemen[(int) (Math.random() * kommunikationsThemen.length)];

                Kommunikation kommunikation = new Kommunikation(
                    "IK" + String.format("%03d", (i * 10) + j),
                    typ,
                    thema,
                    "Detaillierte Beschreibung der " + thema + "-Kommunikation mit dem Interessenten via " + typ + "."
                );
                interessent.addKommunikation(kommunikation);
            }

            // Füge 1-2 zufällige Aktionen hinzu (für jeden Interessenten mindestens eine)
            int anzahlAktionen = 1 + (int) (Math.random() * 2);
            for (int j = 1; j <= anzahlAktionen; j++) {
                String titel = aktionsTitel[(int) (Math.random() * aktionsTitel.length)];

                // Zufälliges Datum in den nächsten 14 Tagen (Interessenten haben kürzere Fristen)
                int tage = (int) (Math.random() * 14);

                Aktion aktion = new Aktion(
                    "IA" + String.format("%03d", (i * 10) + j),
                    titel,
                    "Detaillierte Beschreibung der Interessenten-Aktion: " + titel,
                    LocalDate.now().plusDays(tage)
                );

                // 10% Wahrscheinlichkeit, dass die Aktion bereits erledigt ist
                if (Math.random() < 0.1) {
                    aktion.setErledigt(true);
                }

                interessent.addAktion(aktion);
            }

            // Füge den Interessenten zur Datenbank hinzu
            datenbank.addKunde(interessent);
        }
    }

    /**
     * Erstellt einige heute fällige und überfällige Aktionen für Testzwecke.
     */
    private void erstelleTestAktionen() {
        // Erstelle einen Testkunden für heute fällige Aktionen
        Kunde heuteKunde = new Kunde(
            "K999",
            "Test Heute",
            "Teststraße 1, 12345 Berlin",
            "+49 123 456789",
            "<EMAIL>",
            Kundenkategorie.BESTANDSKUNDE
        );

        // Füge 3 heute fällige Aktionen hinzu
        for (int i = 1; i <= 3; i++) {
            Aktion aktion = new Aktion(
                "AKT-H" + i,
                "Heute fällige Aktion " + i,
                "Diese Aktion ist heute fällig und muss erledigt werden.",
                LocalDate.now() // Heute fällig
            );
            heuteKunde.addAktion(aktion);
        }

        // Füge den Kunden zur Datenbank hinzu
        datenbank.addKunde(heuteKunde);

        // Erstelle einen Testkunden für überfällige Aktionen
        Kunde ueberfaelligKunde = new Kunde(
            "K998",
            "Test Überfällig",
            "Teststraße 2, 12345 Berlin",
            "+49 987 654321",
            "<EMAIL>",
            Kundenkategorie.BESTANDSKUNDE
        );

        // Füge 5 überfällige Aktionen hinzu
        for (int i = 1; i <= 5; i++) {
            Aktion aktion = new Aktion(
                "AKT-U" + i,
                "Überfällige Aktion " + i,
                "Diese Aktion ist überfällig und hätte bereits erledigt werden müssen.",
                LocalDate.now().minusDays(i) // 1-5 Tage überfällig
            );
            ueberfaelligKunde.addAktion(aktion);
        }

        // Füge den Kunden zur Datenbank hinzu
        datenbank.addKunde(ueberfaelligKunde);
    }

    /**
     * Richtet die Benutzeroberflächenkomponenten ein.
     */
    private void setupUI() {
        // Erstelle die Menüleiste mit modernem Aussehen
        JMenuBar menuBar = new JMenuBar();
        menuBar.setBorder(BorderFactory.createEmptyBorder(2, 2, 2, 2));

        JMenu helpMenu = new JMenu("Hilfe");
        helpMenu.setFont(new Font("Segoe UI", Font.PLAIN, 13));

        JMenuItem aboutMenuItem = new JMenuItem("Über das CRM-System");
        aboutMenuItem.setFont(new Font("Segoe UI", Font.PLAIN, 13));

        aboutMenuItem.addActionListener(e -> showAboutDialog());

        helpMenu.add(aboutMenuItem);
        menuBar.add(helpMenu);
        setJMenuBar(menuBar);

        // Erstelle das Hauptpanel mit BorderLayout und modernem Aussehen
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.setBackground(new Color(245, 245, 250));

        // Erstelle das Suchpanel mit modernem Aussehen
        searchPanel = new JPanel(new BorderLayout(10, 0));
        searchPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 10, 0));
        searchPanel.setBackground(new Color(245, 245, 250));

        searchField = new JTextField();
        searchField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(180, 180, 180), 1, true),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));

        JButton searchButton = new JButton("Suchen");
        searchButton.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        searchButton.setBackground(new Color(70, 130, 180));
        searchButton.setForeground(Color.WHITE);
        searchButton.setBorder(BorderFactory.createEmptyBorder(5, 15, 5, 15));
        searchButton.setFocusPainted(false);

        JLabel searchLabel = new JLabel("Nach Namen suchen: ");
        searchLabel.setFont(new Font("Segoe UI", Font.PLAIN, 13));

        searchPanel.add(searchLabel, BorderLayout.WEST);
        searchPanel.add(searchField, BorderLayout.CENTER);
        searchPanel.add(searchButton, BorderLayout.EAST);

        // Erstelle das Kundenlisten-Panel mit Tabs für verschiedene Kategorien
        kundenTabs = new JTabbedPane();
        kundenTabs.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        kundenTabs.setBackground(new Color(245, 245, 250));
        kundenTabs.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        // Erstelle Listen für Bestandskunden und Interessenten
        bestandskundenModel = new DefaultListModel<>();
        interessentenModel = new DefaultListModel<>();

        bestandskundenListe = new JList<>(bestandskundenModel);
        interessentenListe = new JList<>(interessentenModel);

        // Moderne Styling für die Listen
        bestandskundenListe.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        bestandskundenListe.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        bestandskundenListe.setFixedCellHeight(30); // Höhere Zellen für bessere Lesbarkeit
        bestandskundenListe.setSelectionBackground(new Color(70, 130, 180));
        bestandskundenListe.setSelectionForeground(Color.WHITE);
        bestandskundenListe.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        interessentenListe.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        interessentenListe.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        interessentenListe.setFixedCellHeight(30); // Höhere Zellen für bessere Lesbarkeit
        interessentenListe.setSelectionBackground(new Color(70, 130, 180));
        interessentenListe.setSelectionForeground(Color.WHITE);
        interessentenListe.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        // Moderne ScrollPanes mit abgerundeten Ecken
        JScrollPane bestandskundenScrollPane = new JScrollPane(bestandskundenListe);
        bestandskundenScrollPane.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200), 1, true),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        bestandskundenScrollPane.getViewport().setBackground(Color.WHITE);

        JScrollPane interessentenScrollPane = new JScrollPane(interessentenListe);
        interessentenScrollPane.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200), 1, true),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        interessentenScrollPane.getViewport().setBackground(Color.WHITE);

        // Erstelle das Details-Panel mit modernem Aussehen
        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        detailsArea.setLineWrap(true);
        detailsArea.setWrapStyleWord(true);
        detailsArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        detailsArea.setBackground(new Color(252, 252, 252));

        detailsScrollPane = new JScrollPane(detailsArea);
        detailsScrollPane.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(new Color(200, 200, 200), 1, true),
                "Kundendetails",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new Font("Segoe UI", Font.BOLD, 13),
                new Color(70, 130, 180)
            ),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));

        // Erstelle das Kalender-Panel und übergebe den Detailbereich
        kalenderPanel = new KalenderPanel(datenbank, detailsArea, aktionMitKunde -> {
            // Speichere die ausgewählte Aktion
            ausgewaehlteAktion = aktionMitKunde;

            // Aktiviere die Aktionen-Buttons
            markiereErledigtButton.setEnabled(!aktionMitKunde.getAktion().isErledigt());
            loescheAktionButton.setEnabled(true);
        });

        // Erstelle das Statistik-Panel
        statistikPanel = new StatistikPanel(datenbank);

        kundenTabs.addTab("Bestandskunden", bestandskundenScrollPane);
        kundenTabs.addTab("Interessenten", interessentenScrollPane);
        kundenTabs.addTab("Kalender", kalenderPanel);
        kundenTabs.addTab("Statistik", statistikPanel);

        // Füge einen Tab-Wechsel-Listener hinzu
        kundenTabs.addChangeListener(e -> {
            int selectedIndex = kundenTabs.getSelectedIndex();
            // Bei jedem Tab-Wechsel:
            // 1. Die ausgewählte Aktion zurücksetzen
            ausgewaehlteAktion = null;
            // 2. Die Aktionen-Buttons deaktivieren
            markiereErledigtButton.setEnabled(false);
            loescheAktionButton.setEnabled(false);
            // 3. Den Detailbereich leeren
            detailsArea.setText("");
            // 4. Die Aktionen-Tabelle zurücksetzen
            resetAktionenPanel();

            if (selectedIndex == 2) { // Kalender-Tab
                // Ändere den Titel des Details-Panels
                detailsScrollPane.setBorder(BorderFactory.createTitledBorder("Aktionendetails"));
                // Leere den Detailbereich mit der Standard-Nachricht
                detailsArea.setText("Wählen Sie einen Tag im Kalender, um die Aktionen anzuzeigen.");
                // Setze den aktuellen Kunden zurück
                aktuellerKunde = null;
                // Blende die Suchleiste aus
                searchPanel.setVisible(false);
                // Blende den Details-Bereich wieder ein, falls er ausgeblendet war
                detailsScrollPane.setVisible(true);
                // Aktiviere den Divider wieder, falls er deaktiviert war
                horizontalSplitPane.setEnabled(true);
                // Setze die Divider-Position auf einen Wert, der den gesamten Kalender sichtbar macht
                // Verwende einen größeren Wert (600), um mehr Platz für den Kalender zu schaffen und Sonntag sichtbar zu machen
                horizontalSplitPane.setDividerLocation(600);
                // Zeige die Kalender-Buttons an
                ((CardLayout) buttonPanel.getLayout()).show(buttonPanel, KALENDER_BUTTONS);
                // Leere die Aktionen-Anzeige im Kalender-Panel
                kalenderPanel.leereAktionenAnzeige();
                // Aktualisiere die Aktionen im Kalender-Panel
                kalenderPanel.aktualisieren();
                // Optimiere die Divider-Position für eine gute Darstellung
                kalenderPanel.optimiereDividerPosition();
            } else if (selectedIndex == 3) { // Statistik-Tab
                // Blende die Suchleiste aus
                searchPanel.setVisible(false);
                // Blende den Details-Bereich aus
                detailsScrollPane.setVisible(false);
                // Setze den aktuellen Kunden zurück
                aktuellerKunde = null;
                // Zeige die Statistik-Buttons an
                ((CardLayout) buttonPanel.getLayout()).show(buttonPanel, STATISTIK_BUTTONS);
                // Aktualisiere die Statistik
                statistikPanel.aktualisieren();
                // Setze die Divider-Position auf Maximum, damit der Statistik-Tab den gesamten Bereich einnimmt
                horizontalSplitPane.setDividerLocation(horizontalSplitPane.getWidth());
                // Deaktiviere den Divider, damit er nicht verschoben werden kann
                horizontalSplitPane.setEnabled(false);
            } else {
                // Setze den Titel zurück
                detailsScrollPane.setBorder(BorderFactory.createTitledBorder("Kundendetails"));
                // Blende die Suchleiste ein
                searchPanel.setVisible(true);
                // Blende den Details-Bereich wieder ein
                detailsScrollPane.setVisible(true);
                // Aktiviere den Divider wieder
                horizontalSplitPane.setEnabled(true);
                // Setze die Divider-Position zurück
                horizontalSplitPane.setDividerLocation(600);
                // Zeige die Kunden-Buttons an
                ((CardLayout) buttonPanel.getLayout()).show(buttonPanel, KUNDEN_BUTTONS);
                // Setze den aktuellen Kunden zurück, wenn zwischen Kunden-Tabs gewechselt wird
                if (selectedIndex == 0) { // Bestandskunden
                    // Deselektiere die Interessenten-Liste
                    interessentenListe.clearSelection();
                    // Wenn ein Bestandskunde ausgewählt ist, zeige dessen Details an
                    if (bestandskundenListe.getSelectedIndex() >= 0) {
                        displaySelectedCustomerDetails(bestandskundenListe);
                    } else {
                        detailsArea.setText("Wählen Sie einen Kunden aus der Liste aus.");
                    }
                } else { // Interessenten
                    // Deselektiere die Bestandskunden-Liste
                    bestandskundenListe.clearSelection();
                    // Wenn ein Interessent ausgewählt ist, zeige dessen Details an
                    if (interessentenListe.getSelectedIndex() >= 0) {
                        displaySelectedCustomerDetails(interessentenListe);
                    } else {
                        detailsArea.setText("Wählen Sie einen Interessenten aus der Liste aus.");
                    }
                }
            }
        });

        // Für die Kompatibilität mit bestehenden Methoden behalten wir die allgemeine Liste bei
        listModel = new DefaultListModel<>();
        kundenListe = new JList<>(listModel);

        // Aktualisiere alle Kundenlisten
        updateCustomerLists(datenbank.getAlleKunden());

        // Das Details-Panel wurde bereits erstellt

        // Das Aktionen-Panel wird jetzt im KalenderPanel erstellt

        // Erstelle das Button-Panel für Kunden-Tabs mit modernem Aussehen
        JPanel kundenButtonPanel = new JPanel(new GridLayout(1, 5, 10, 10));
        kundenButtonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));
        kundenButtonPanel.setBackground(new Color(245, 245, 250));

        // Erstelle moderne Buttons mit einheitlichem Styling
        addButton = createStyledButton("Kunde hinzufügen", new Color(70, 130, 180));
        editButton = createStyledButton("Kunde bearbeiten", new Color(70, 130, 180));
        deleteButton = createStyledButton("Kunde löschen", new Color(220, 53, 69)); // Rot für Löschen
        addKommunikationButton = createStyledButton("Kommunikation hinzufügen", new Color(70, 130, 180));
        addAktionButton = createStyledButton("Aktion hinzufügen", new Color(70, 130, 180));

        kundenButtonPanel.add(addButton);
        kundenButtonPanel.add(editButton);
        kundenButtonPanel.add(deleteButton);
        kundenButtonPanel.add(addKommunikationButton);
        kundenButtonPanel.add(addAktionButton);

        // Erstelle das Button-Panel für den Kalender-Tab mit modernem Aussehen
        JPanel kalenderButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        kalenderButtonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));
        kalenderButtonPanel.setBackground(new Color(245, 245, 250));

        JButton kalenderAktionButton = createStyledButton("Aktion hinzufügen", new Color(70, 130, 180));
        markiereErledigtButton = createStyledButton("Als erledigt markieren", new Color(40, 167, 69)); // Grün für "erledigt"
        loescheAktionButton = createStyledButton("Aktion löschen", new Color(220, 53, 69)); // Rot für Löschen

        // Deaktiviere die Aktionen-Buttons initial
        markiereErledigtButton.setEnabled(false);
        loescheAktionButton.setEnabled(false);

        kalenderButtonPanel.add(kalenderAktionButton);
        kalenderButtonPanel.add(markiereErledigtButton);
        kalenderButtonPanel.add(loescheAktionButton);

        // Füge den gleichen ActionListener zum Kalender-Button hinzu
        kalenderAktionButton.addActionListener(e -> addAktionButton.doClick());

        // Füge ActionListener für die Aktionen-Buttons hinzu
        markiereErledigtButton.addActionListener(e -> {
            if (ausgewaehlteAktion != null) {
                markiereAktionAlsErledigt(ausgewaehlteAktion.getKunde(), ausgewaehlteAktion.getAktion().getId());
                // Nach dem Markieren als erledigt, aktualisiere die Anzeige
                kalenderPanel.aktualisieren();
                // Deaktiviere die Buttons, da die Aktion jetzt erledigt ist
                markiereErledigtButton.setEnabled(false);
                loescheAktionButton.setEnabled(false);
                // Setze die ausgewählte Aktion zurück
                ausgewaehlteAktion = null;
            }
        });

        loescheAktionButton.addActionListener(e -> {
            if (ausgewaehlteAktion != null) {
                loescheAktion(ausgewaehlteAktion.getKunde(), ausgewaehlteAktion.getAktion().getId());
                // Nach dem Löschen, aktualisiere die Anzeige
                kalenderPanel.aktualisieren();
                // Deaktiviere die Buttons, da die Aktion jetzt gelöscht ist
                markiereErledigtButton.setEnabled(false);
                loescheAktionButton.setEnabled(false);
                // Setze die ausgewählte Aktion zurück
                ausgewaehlteAktion = null;
            }
        });

        // Erstelle das Button-Panel für den Statistik-Tab mit modernem Aussehen
        JPanel statistikButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        statistikButtonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));
        statistikButtonPanel.setBackground(new Color(245, 245, 250));

        JButton exportierenButton = createStyledButton("Exportieren", new Color(70, 130, 180));
        JButton druckenButton = createStyledButton("Drucken", new Color(70, 130, 180));

        statistikButtonPanel.add(exportierenButton);
        statistikButtonPanel.add(druckenButton);

        // Füge ActionListener für die Statistik-Buttons hinzu
        exportierenButton.addActionListener(e -> {
            // Delegiere die Aktion an das BerichtePanel im StatistikPanel
            if (statistikPanel != null) {
                statistikPanel.exportiereBericht();
            }
        });

        druckenButton.addActionListener(e -> {
            // Delegiere die Aktion an das BerichtePanel im StatistikPanel
            if (statistikPanel != null) {
                statistikPanel.druckeBericht();
            }
        });

        // Erstelle ein CardLayout-Panel für die verschiedenen Button-Panels
        buttonPanel = new JPanel(new CardLayout());
        buttonPanel.add(kundenButtonPanel, KUNDEN_BUTTONS);
        buttonPanel.add(kalenderButtonPanel, KALENDER_BUTTONS);
        buttonPanel.add(statistikButtonPanel, STATISTIK_BUTTONS);

        // Erstelle ein Split-Panel für die Liste und Details mit modernem Aussehen
        horizontalSplitPane = new JSplitPane(
            JSplitPane.HORIZONTAL_SPLIT,
            kundenTabs,
            detailsScrollPane
        );
        // Verbessere das Aussehen des SplitPane
        horizontalSplitPane.setBorder(BorderFactory.createEmptyBorder());
        horizontalSplitPane.setDividerSize(7); // Schmalerer Divider für ein moderneres Aussehen
        horizontalSplitPane.setUI(new javax.swing.plaf.basic.BasicSplitPaneUI() {
            @Override
            public javax.swing.plaf.basic.BasicSplitPaneDivider createDefaultDivider() {
                return new javax.swing.plaf.basic.BasicSplitPaneDivider(this) {
                    public void setBorder(Border b) {
                        // Keine Border für den Divider
                    }

                    @Override
                    public void paint(Graphics g) {
                        // Zeichne einen modernen, subtilen Divider
                        Graphics2D g2 = (Graphics2D) g;
                        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                        g2.setColor(new Color(220, 220, 220));
                        g2.fillRect(0, 0, getWidth(), getHeight());

                        // Zeichne eine subtile Linie in der Mitte
                        g2.setColor(new Color(200, 200, 200));
                        if (orientation == JSplitPane.HORIZONTAL_SPLIT) {
                            g2.fillRect(3, 0, 1, getHeight());
                        } else {
                            g2.fillRect(0, 3, getWidth(), 1);
                        }
                    }
                };
            }
        });

        // Setze eine größere initiale Divider-Position, um mehr Platz für den Kalender zu schaffen
        horizontalSplitPane.setDividerLocation(600);
        horizontalSplitPane.setContinuousLayout(true); // Für flüssigeres Verschieben

        // Füge Komponenten zum Hauptpanel hinzu
        mainPanel.add(searchPanel, BorderLayout.NORTH);
        mainPanel.add(horizontalSplitPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        // Füge das Hauptpanel zum Frame hinzu
        add(mainPanel);

        // Füge Event-Listener hinzu
        ActionListener searchAction = new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String searchTerm = searchField.getText();
                List<Kunde> results = datenbank.filterByName(searchTerm);
                updateCustomerLists(results);
            }
        };

        // Füge den ActionListener zum Suchbutton hinzu
        searchButton.addActionListener(searchAction);

        // Füge einen KeyListener zum Suchfeld hinzu, um die Suche auch mit Enter zu starten
        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    // Führe die gleiche Aktion aus wie beim Klick auf den Suchbutton
                    searchAction.actionPerformed(new ActionEvent(searchField, ActionEvent.ACTION_PERFORMED, "Enter"));
                }
            }
        });

        // Füge Listener für beide Listen hinzu
        bestandskundenListe.addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    // Deselektiere die andere Liste
                    interessentenListe.clearSelection();
                    displaySelectedCustomerDetails(bestandskundenListe);
                }
            }
        });

        interessentenListe.addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    // Deselektiere die andere Liste
                    bestandskundenListe.clearSelection();
                    displaySelectedCustomerDetails(interessentenListe);
                }
            }
        });

        // Füge Button-Listener hinzu
        addButton.addActionListener(e -> showAddCustomerDialog());
        editButton.addActionListener(e -> showEditCustomerDialog());
        deleteButton.addActionListener(e -> deleteSelectedCustomer());
        addKommunikationButton.addActionListener(e -> showAddKommunikationDialog());
        addAktionButton.addActionListener(e -> showAddAktionDialog());
    }

    /**
     * Aktualisiert alle Kundenlisten mit den angegebenen Kunden.
     *
     * @param kunden Die Liste der anzuzeigenden Kunden
     */
    private void updateCustomerLists(List<Kunde> kunden) {
        // Leere alle Listen
        bestandskundenModel.clear();
        interessentenModel.clear();
        listModel.clear();

        // Fülle die Listen basierend auf der Kundenkategorie
        for (Kunde kunde : kunden) {
            String displayText = kunde.getId() + ": " + kunde.getName();

            // Füge den Kunden zur allgemeinen Liste hinzu
            listModel.addElement(displayText);

            // Füge den Kunden zur entsprechenden kategorisierten Liste hinzu
            if (kunde.getKategorie() == Kundenkategorie.BESTANDSKUNDE) {
                bestandskundenModel.addElement(displayText);
            } else if (kunde.getKategorie() == Kundenkategorie.INTERESSENT) {
                interessentenModel.addElement(displayText);
            }
        }
    }

    /**
     * Aktualisiert die Kundenliste mit den angegebenen Kunden (für Kompatibilität mit bestehenden Methoden).
     *
     * @param kunden Die Liste der anzuzeigenden Kunden
     */
    private void updateCustomerList(List<Kunde> kunden) {
        updateCustomerLists(kunden);
    }

    /**
     * Setzt das Aktionen-Panel zurück, indem die Aktionen-Tabelle geleert wird.
     * Das Panel bleibt in der GUI vorhanden, aber ohne Inhalt.
     */
    private void resetAktionenPanel() {
        // Erstelle ein leeres TableModel mit den gleichen Spalten
        String[] columnNames = {"Fällig am", "Titel", "Status", "ID"};
        aktionenTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Keine Bearbeitung in der Tabelle erlauben
            }
        };

        // Wenn die Tabelle bereits existiert, aktualisiere sie
        if (aktionenTable != null) {
            aktionenTable.setModel(aktionenTableModel);

            // Verstecke die ID-Spalte (wird nur für interne Zwecke verwendet)
            if (aktionenTable.getColumnCount() >= 4) {
                aktionenTable.getColumnModel().getColumn(3).setMinWidth(0);
                aktionenTable.getColumnModel().getColumn(3).setMaxWidth(0);
                aktionenTable.getColumnModel().getColumn(3).setWidth(0);
            }
        }
    }

    /**
     * Zeigt die Details des ausgewählten Kunden aus der angegebenen Liste an.
     * Aktualisiert nur die Anzeige, wenn nicht der Kalender-Tab aktiv ist.
     *
     * @param list Die JList, aus der der Kunde ausgewählt wurde
     */
    private void displaySelectedCustomerDetails(JList<String> list) {
        // Wenn der Kalender-Tab aktiv ist, keine Kundendetails anzeigen
        if (kundenTabs.getSelectedIndex() == 2) {
            return;
        }
        int selectedIndex = list.getSelectedIndex();
        if (selectedIndex >= 0) {
            String selectedItem = list.getSelectedValue();
            String id = selectedItem.split(":")[0].trim();

            Kunde kunde = datenbank.getKundeById(id);
            if (kunde != null) {
                StringBuilder details = new StringBuilder();
                details.append("ID: ").append(kunde.getId()).append("\n");
                details.append("Name: ").append(kunde.getName()).append("\n");
                details.append("Adresse: ").append(kunde.getAdresse()).append("\n");
                details.append("Telefon: ").append(kunde.getTelefonnummer()).append("\n");
                details.append("E-Mail: ").append(kunde.getEmail()).append("\n");
                details.append("Kategorie: ").append(kunde.getKategorie()).append("\n\n");

                details.append("Kommunikationen:\n");
                List<Kommunikation> kommunikationen = kunde.getKommunikationen();
                if (kommunikationen.isEmpty()) {
                    details.append("  Keine Kommunikationen erfasst.\n");
                } else {
                    for (Kommunikation k : kommunikationen) {
                        details.append("  - ").append(k.getFormattiertesDatum()).append(": ")
                               .append(k.getTyp()).append(" - ")
                               .append(k.getBeschreibung()).append("\n");
                    }
                }

                // Speichere den aktuellen Kunden
                aktuellerKunde = kunde;

                // Erstelle eine Tabelle für Aktionen
                String[] columnNames = {"Fällig am", "Titel", "Status", "ID"};
                aktionenTableModel = new DefaultTableModel(columnNames, 0) {
                    @Override
                    public boolean isCellEditable(int row, int column) {
                        return false; // Keine Bearbeitung in der Tabelle erlauben
                    }
                };

                // Fülle die Tabelle mit Aktionen
                List<Aktion> aktionen = kunde.getAktionen();
                if (!aktionen.isEmpty()) {
                    for (Aktion a : aktionen) {
                        String datum = a.getFaelligkeitsDatum().format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));
                        String titel = a.getTitel();
                        String status = a.isErledigt() ? "Erledigt" : "Ausstehend";
                        String aktionId = a.getId();

                        aktionenTableModel.addRow(new Object[]{datum, titel, status, aktionId});
                    }
                }

                aktionenTable = new JTable(aktionenTableModel);
                aktionenTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
                aktionenTable.getTableHeader().setReorderingAllowed(false);

                // Setze benutzerdefinierten Renderer für Farben
                aktionenTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
                    @Override
                    public Component getTableCellRendererComponent(JTable table, Object value,
                            boolean isSelected, boolean hasFocus, int row, int column) {
                        Component c = super.getTableCellRendererComponent(
                                table, value, isSelected, hasFocus, row, column);

                        // Hole den Status aus der Tabelle
                        String status = (String) table.getValueAt(row, 2);

                        if ("Ausstehend".equals(status)) {
                            // Prüfe, ob die Aktion überfällig ist
                            String datumStr = (String) table.getValueAt(row, 0);
                            try {
                                LocalDate faelligkeitsDatum = LocalDate.parse(datumStr,
                                        DateTimeFormatter.ofPattern("dd.MM.yyyy"));
                                if (faelligkeitsDatum.isBefore(LocalDate.now())) {
                                    c.setForeground(Color.RED);
                                    setFont(getFont().deriveFont(Font.BOLD));
                                } else if (faelligkeitsDatum.isEqual(LocalDate.now())) {
                                    c.setForeground(new Color(255, 140, 0)); // Orange
                                    setFont(getFont().deriveFont(Font.BOLD));
                                } else {
                                    c.setForeground(table.getForeground());
                                    setFont(getFont().deriveFont(Font.PLAIN));
                                }
                            } catch (Exception e) {
                                c.setForeground(table.getForeground());
                                setFont(getFont().deriveFont(Font.PLAIN));
                            }
                        } else {
                            // Erledigte Aktionen in grün anzeigen
                            c.setForeground(new Color(0, 128, 0)); // Grün
                            setFont(getFont().deriveFont(Font.PLAIN));
                        }

                        return c;
                    }
                });

                // Verstecke die ID-Spalte (wird nur für interne Zwecke verwendet)
                aktionenTable.getColumnModel().getColumn(3).setMinWidth(0);
                aktionenTable.getColumnModel().getColumn(3).setMaxWidth(0);
                aktionenTable.getColumnModel().getColumn(3).setWidth(0);

                JScrollPane aktionenScrollPane = new JScrollPane(aktionenTable);
                aktionenScrollPane.setBorder(BorderFactory.createTitledBorder("Aktionen"));

                // Füge Buttons für Aktionen-Management hinzu
                JPanel aktionenButtonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
                JButton markiereErledigtButton = new JButton("Als erledigt markieren");
                JButton loescheAktionButton = new JButton("Aktion löschen");

                // Deaktiviere Buttons initial
                markiereErledigtButton.setEnabled(false);
                loescheAktionButton.setEnabled(false);

                // Füge Listener für Tabellenauswahl hinzu
                aktionenTable.getSelectionModel().addListSelectionListener(e -> {
                    if (!e.getValueIsAdjusting()) {
                        int selectedRow = aktionenTable.getSelectedRow();
                        if (selectedRow >= 0) {
                            // Aktiviere Buttons, wenn eine Zeile ausgewählt ist
                            String status = (String) aktionenTable.getValueAt(selectedRow, 2);
                            markiereErledigtButton.setEnabled("Ausstehend".equals(status));
                            loescheAktionButton.setEnabled(true);
                        } else {
                            // Deaktiviere Buttons, wenn keine Zeile ausgewählt ist
                            markiereErledigtButton.setEnabled(false);
                            loescheAktionButton.setEnabled(false);
                        }
                    }
                });

                markiereErledigtButton.addActionListener(e -> {
                    int selectedRow = aktionenTable.getSelectedRow();
                    if (selectedRow >= 0) {
                        String aktionId = (String) aktionenTable.getValueAt(selectedRow, 3);
                        markiereAktionAlsErledigt(aktuellerKunde, aktionId);
                    }
                });

                loescheAktionButton.addActionListener(e -> {
                    int selectedRow = aktionenTable.getSelectedRow();
                    if (selectedRow >= 0) {
                        String aktionId = (String) aktionenTable.getValueAt(selectedRow, 3);
                        loescheAktion(aktuellerKunde, aktionId);
                    }
                });

                aktionenButtonPanel.add(markiereErledigtButton);
                aktionenButtonPanel.add(loescheAktionButton);

                // Erstelle ein Panel für die Details, Aktionen-Tabelle und Buttons
                JPanel detailsPanel = new JPanel(new BorderLayout());
                JScrollPane scrollPane = new JScrollPane(detailsArea);
                detailsArea.setText(details.toString());

                // Erstelle ein Panel für die Aktionen-Tabelle und Buttons
                JPanel aktionenPanel = new JPanel(new BorderLayout());
                aktionenPanel.add(aktionenScrollPane, BorderLayout.CENTER);
                aktionenPanel.add(aktionenButtonPanel, BorderLayout.SOUTH);

                // Erstelle ein Split-Panel für Details und Aktionen
                JSplitPane detailsSplitPane = new JSplitPane(
                    JSplitPane.VERTICAL_SPLIT,
                    scrollPane,
                    aktionenPanel
                );
                detailsSplitPane.setDividerLocation(200);

                detailsPanel.add(detailsSplitPane, BorderLayout.CENTER);

                // Ersetze den Inhalt des Details-Bereichs
                Component[] components = detailsScrollPane.getViewport().getComponents();
                if (components.length > 0) {
                    detailsScrollPane.getViewport().remove(components[0]);
                }
                detailsScrollPane.getViewport().add(detailsPanel);
            }
        } else {
            detailsArea.setText("");
        }
    }

    /**
     * Zeigt einen Dialog zum Hinzufügen eines neuen Kunden an.
     */
    private void showAddCustomerDialog() {
        // Erstelle ein Panel mit einem Rasterlayout für die Formularfelder
        JPanel panel = new JPanel(new GridLayout(7, 2, 5, 5));

        // Erstelle Textfelder für Kundeninformationen
        JTextField idField = new JTextField(10);
        JTextField vornameField = new JTextField(20);
        JTextField nachnameField = new JTextField(20);
        JTextField addressField = new JTextField(30);
        JTextField phoneField = new JTextField(15);
        JTextField emailField = new JTextField(20);
        JComboBox<Kundenkategorie> categoryComboBox = new JComboBox<>(Kundenkategorie.values());

        // Generiere eine eindeutige ID
        String newId = generateUniqueCustomerId();
        idField.setText(newId);
        idField.setEditable(false); // Erlaube keine Bearbeitung der ID

        // Füge Labels und Felder zum Panel hinzu
        panel.add(new JLabel("ID:"));
        panel.add(idField);
        panel.add(new JLabel("Vorname:"));
        panel.add(vornameField);
        panel.add(new JLabel("Nachname:"));
        panel.add(nachnameField);
        panel.add(new JLabel("Adresse:"));
        panel.add(addressField);
        panel.add(new JLabel("Telefon:"));
        panel.add(phoneField);
        panel.add(new JLabel("E-Mail:"));
        panel.add(emailField);
        panel.add(new JLabel("Kategorie:"));
        panel.add(categoryComboBox);

        // Zeige den Dialog in einer Schleife an, bis alle Felder ausgefüllt sind oder der Benutzer abbricht
        boolean isValid = false;
        boolean userCancelled = false;

        while (!isValid && !userCancelled) {
            int result = JOptionPane.showConfirmDialog(
                this,
                panel,
                "Neuen Kunden hinzufügen",
                JOptionPane.OK_CANCEL_OPTION,
                JOptionPane.PLAIN_MESSAGE
            );

            if (result == JOptionPane.OK_OPTION) {
                // Validiere alle Eingabefelder und markiere fehlende Felder rot
                String validationError = validateCustomerFields(
                    vornameField,
                    nachnameField,
                    addressField,
                    phoneField,
                    emailField
                );

                if (validationError != null) {
                    JOptionPane.showMessageDialog(
                        this,
                        validationError,
                        "Validierungsfehler",
                        JOptionPane.ERROR_MESSAGE
                    );
                    // Schleife wird fortgesetzt, Dialog wird erneut angezeigt
                } else {
                    isValid = true; // Alle Felder sind ausgefüllt, Schleife beenden
                }
            } else {
                userCancelled = true; // Benutzer hat abgebrochen, Schleife beenden
            }
        }

        // Wenn alle Felder ausgefüllt sind und der Benutzer nicht abgebrochen hat
        if (isValid) {

            // Erstelle einen neuen Kunden mit kombiniertem Vor- und Nachnamen
            Kunde newKunde = new Kunde(
                idField.getText(),
                vornameField.getText() + " " + nachnameField.getText(),
                addressField.getText(),
                phoneField.getText(),
                emailField.getText(),
                (Kundenkategorie) categoryComboBox.getSelectedItem()
            );

            // Füge den Kunden zur Datenbank hinzu
            boolean added = datenbank.addKunde(newKunde);

            if (added) {
                // Aktualisiere die Kundenliste
                updateCustomerList(datenbank.getAlleKunden());
                kalenderPanel.aktualisieren();
                JOptionPane.showMessageDialog(
                    this,
                    "Kunde erfolgreich hinzugefügt.",
                    "Erfolg",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } else {
                JOptionPane.showMessageDialog(
                    this,
                    "Kunde konnte nicht hinzugefügt werden. ID existiert möglicherweise bereits.",
                    "Fehler",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    /**
     * Generiert eine eindeutige Kunden-ID.
     *
     * @return Eine eindeutige Kunden-ID
     */
    private String generateUniqueCustomerId() {
        // Hole die aktuelle Anzahl der Kunden
        int count = datenbank.getAnzahlKunden() + 1;

        // Format: K gefolgt von einer 3-stelligen Zahl
        return String.format("K%03d", count);
    }

    /**
     * Zeigt einen Dialog zum Bearbeiten eines vorhandenen Kunden an.
     */
    private void showEditCustomerDialog() {
        // Prüfe, ob ein Kunde ausgewählt ist
        JList<String> activeList = null;
        if (bestandskundenListe.getSelectedIndex() >= 0) {
            activeList = bestandskundenListe;
        } else if (interessentenListe.getSelectedIndex() >= 0) {
            activeList = interessentenListe;
        }

        if (activeList == null) {
            JOptionPane.showMessageDialog(
                this,
                "Bitte wählen Sie zuerst einen Kunden aus.",
                "Kein Kunde ausgewählt",
                JOptionPane.WARNING_MESSAGE
            );
            return;
        }

        // Hole die ID des ausgewählten Kunden
        String selectedItem = activeList.getSelectedValue();
        String id = selectedItem.split(":")[0].trim();

        // Hole den Kunden aus der Datenbank
        Kunde kunde = datenbank.getKundeById(id);
        if (kunde == null) {
            JOptionPane.showMessageDialog(
                this,
                "Der ausgewählte Kunde konnte nicht gefunden werden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }

        // Erstelle ein Panel mit einem Rasterlayout für die Formularfelder
        JPanel panel = new JPanel(new GridLayout(7, 2, 5, 5));

        // Erstelle Textfelder für Kundeninformationen und fülle sie mit den aktuellen Werten
        JTextField idField = new JTextField(kunde.getId(), 10);

        // Teile den Namen in Vor- und Nachname auf
        String fullName = kunde.getName();
        String vorname = "";
        String nachname = "";

        if (fullName != null && !fullName.isEmpty()) {
            int spaceIndex = fullName.indexOf(' ');
            if (spaceIndex > 0) {
                vorname = fullName.substring(0, spaceIndex);
                nachname = fullName.substring(spaceIndex + 1);
            } else {
                // Falls kein Leerzeichen gefunden wurde, setze den ganzen Namen als Vorname
                vorname = fullName;
            }
        }

        JTextField vornameField = new JTextField(vorname, 20);
        JTextField nachnameField = new JTextField(nachname, 20);
        JTextField addressField = new JTextField(kunde.getAdresse(), 30);
        JTextField phoneField = new JTextField(kunde.getTelefonnummer(), 15);
        JTextField emailField = new JTextField(kunde.getEmail(), 20);
        JComboBox<Kundenkategorie> categoryComboBox = new JComboBox<>(Kundenkategorie.values());
        categoryComboBox.setSelectedItem(kunde.getKategorie());

        // ID kann nicht bearbeitet werden
        idField.setEditable(false);

        // Füge Labels und Felder zum Panel hinzu
        panel.add(new JLabel("ID:"));
        panel.add(idField);
        panel.add(new JLabel("Vorname:"));
        panel.add(vornameField);
        panel.add(new JLabel("Nachname:"));
        panel.add(nachnameField);
        panel.add(new JLabel("Adresse:"));
        panel.add(addressField);
        panel.add(new JLabel("Telefon:"));
        panel.add(phoneField);
        panel.add(new JLabel("E-Mail:"));
        panel.add(emailField);
        panel.add(new JLabel("Kategorie:"));
        panel.add(categoryComboBox);

        // Zeige den Dialog in einer Schleife an, bis alle Felder ausgefüllt sind oder der Benutzer abbricht
        boolean isValid = false;
        boolean userCancelled = false;

        while (!isValid && !userCancelled) {
            int result = JOptionPane.showConfirmDialog(
                this,
                panel,
                "Kunden bearbeiten",
                JOptionPane.OK_CANCEL_OPTION,
                JOptionPane.PLAIN_MESSAGE
            );

            if (result == JOptionPane.OK_OPTION) {
                // Validiere alle Eingabefelder und markiere fehlende Felder rot
                String validationError = validateCustomerFields(
                    vornameField,
                    nachnameField,
                    addressField,
                    phoneField,
                    emailField
                );

                if (validationError != null) {
                    JOptionPane.showMessageDialog(
                        this,
                        validationError,
                        "Validierungsfehler",
                        JOptionPane.ERROR_MESSAGE
                    );
                    // Schleife wird fortgesetzt, Dialog wird erneut angezeigt
                } else {
                    isValid = true; // Alle Felder sind ausgefüllt, Schleife beenden
                }
            } else {
                userCancelled = true; // Benutzer hat abgebrochen, Schleife beenden
            }
        }

        // Wenn alle Felder ausgefüllt sind und der Benutzer nicht abgebrochen hat
        if (isValid) {

            // Aktualisiere den Kunden mit den neuen Werten
            kunde.setName(vornameField.getText() + " " + nachnameField.getText());
            kunde.setAdresse(addressField.getText());
            kunde.setTelefonnummer(phoneField.getText());
            kunde.setEmail(emailField.getText());
            kunde.setKategorie((Kundenkategorie) categoryComboBox.getSelectedItem());

            // Aktualisiere den Kunden in der Datenbank
            boolean updated = datenbank.updateKunde(kunde);

            if (updated) {
                // Aktualisiere die Kundenliste und Details
                updateCustomerLists(datenbank.getAlleKunden());
                displaySelectedCustomerDetails();
                kalenderPanel.aktualisieren();

                JOptionPane.showMessageDialog(
                    this,
                    "Kunde erfolgreich aktualisiert.",
                    "Erfolg",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } else {
                JOptionPane.showMessageDialog(
                    this,
                    "Kunde konnte nicht aktualisiert werden.",
                    "Fehler",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    /**
     * Zeigt die Details des ausgewählten Kunden an (für Kompatibilität mit bestehenden Methoden).
     */
    private void displaySelectedCustomerDetails() {
        // Prüfe, welche Liste einen ausgewählten Eintrag hat
        if (bestandskundenListe.getSelectedIndex() >= 0) {
            displaySelectedCustomerDetails(bestandskundenListe);
        } else if (interessentenListe.getSelectedIndex() >= 0) {
            displaySelectedCustomerDetails(interessentenListe);
        }
    }

    /**
     * Löscht den ausgewählten Kunden nach Bestätigung.
     */
    private void deleteSelectedCustomer() {
        // Prüfe, ob ein Kunde ausgewählt ist
        JList<String> activeList = null;
        if (bestandskundenListe.getSelectedIndex() >= 0) {
            activeList = bestandskundenListe;
        } else if (interessentenListe.getSelectedIndex() >= 0) {
            activeList = interessentenListe;
        }

        if (activeList == null) {
            JOptionPane.showMessageDialog(
                this,
                "Bitte wählen Sie zuerst einen Kunden aus.",
                "Kein Kunde ausgewählt",
                JOptionPane.WARNING_MESSAGE
            );
            return;
        }

        // Hole die ID des ausgewählten Kunden
        String selectedItem = activeList.getSelectedValue();
        String id = selectedItem.split(":")[0].trim();
        String name = selectedItem.substring(selectedItem.indexOf(":") + 1).trim();

        // Bestätigungsdialog anzeigen
        int confirm = JOptionPane.showConfirmDialog(
            this,
            "Sind Sie sicher, dass Sie den Kunden '" + name + "' löschen möchten?",
            "Kunde löschen",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        );

        if (confirm == JOptionPane.YES_OPTION) {
            // Lösche den Kunden aus der Datenbank
            boolean removed = datenbank.removeKunde(id);

            if (removed) {
                // Aktualisiere die Kundenliste
                updateCustomerLists(datenbank.getAlleKunden());
                kalenderPanel.aktualisieren();
                // Lösche die Details
                detailsArea.setText("");

                JOptionPane.showMessageDialog(
                    this,
                    "Kunde erfolgreich gelöscht.",
                    "Erfolg",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } else {
                JOptionPane.showMessageDialog(
                    this,
                    "Kunde konnte nicht gelöscht werden.",
                    "Fehler",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    /**
     * Zeigt einen Dialog zum Hinzufügen einer neuen Kommunikation für einen Kunden an.
     */
    private void showAddKommunikationDialog() {
        // Prüfe, ob ein Kunde ausgewählt ist
        JList<String> activeList = null;
        if (bestandskundenListe.getSelectedIndex() >= 0) {
            activeList = bestandskundenListe;
        } else if (interessentenListe.getSelectedIndex() >= 0) {
            activeList = interessentenListe;
        }

        if (activeList == null) {
            JOptionPane.showMessageDialog(
                this,
                "Bitte wählen Sie zuerst einen Kunden aus.",
                "Kein Kunde ausgewählt",
                JOptionPane.WARNING_MESSAGE
            );
            return;
        }

        // Hole die ID des ausgewählten Kunden
        String selectedItem = activeList.getSelectedValue();
        String id = selectedItem.split(":")[0].trim();

        // Hole den Kunden aus der Datenbank
        Kunde kunde = datenbank.getKundeById(id);
        if (kunde == null) {
            JOptionPane.showMessageDialog(
                this,
                "Der ausgewählte Kunde konnte nicht gefunden werden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }

        // Erstelle ein Panel mit einem Rasterlayout für die Formularfelder
        JPanel panel = new JPanel(new GridLayout(5, 2, 5, 5));

        // Zeige den ausgewählten Kunden an
        JLabel kundeLabel = new JLabel("Ausgewählter Kunde: " + kunde.getName());
        kundeLabel.setFont(kundeLabel.getFont().deriveFont(Font.BOLD));
        panel.add(kundeLabel);
        panel.add(new JLabel()); // Leere Zelle für das Layout

        // Erstelle Textfelder und Auswahlfelder für die Kommunikation
        JTextField idField = new JTextField(10);
        JComboBox<KommunikationsTyp> typComboBox = new JComboBox<>(KommunikationsTyp.values());
        JTextField beschreibungField = new JTextField(30);
        JTextArea notizenArea = new JTextArea(5, 30);
        JScrollPane notizenScrollPane = new JScrollPane(notizenArea);

        // Generiere eine eindeutige ID für die Kommunikation
        String newId = "KOM" + String.format("%03d", kunde.getKommunikationen().size() + 1);
        idField.setText(newId);
        idField.setEditable(false); // Erlaube keine Bearbeitung der ID

        // Sonstiges-Typ Handling
        JTextField customTypField = new JTextField(20);
        customTypField.setEnabled(false);

        typComboBox.addActionListener(e -> {
            KommunikationsTyp selectedType = (KommunikationsTyp) typComboBox.getSelectedItem();
            customTypField.setEnabled(selectedType == KommunikationsTyp.SONSTIGES);
        });

        // Erstelle ein Panel für den Typ mit Combo-Box und Custom-Feld
        JPanel typPanel = new JPanel(new BorderLayout());
        typPanel.add(typComboBox, BorderLayout.CENTER);
        typPanel.add(customTypField, BorderLayout.EAST);

        // Füge Labels und Felder zum Panel hinzu
        panel.add(new JLabel("ID:"));
        panel.add(idField);
        panel.add(new JLabel("Typ:"));
        panel.add(typPanel);
        panel.add(new JLabel("Beschreibung:"));
        panel.add(beschreibungField);
        panel.add(new JLabel("Notizen:"));
        panel.add(notizenScrollPane);

        // Zeige den Dialog an
        int result = JOptionPane.showConfirmDialog(
            this,
            panel,
            "Neue Kommunikation hinzufügen",
            JOptionPane.OK_CANCEL_OPTION,
            JOptionPane.PLAIN_MESSAGE
        );

        // Verarbeite das Ergebnis
        if (result == JOptionPane.OK_OPTION) {
            // Validiere Eingabe
            if (beschreibungField.getText().trim().isEmpty()) {
                JOptionPane.showMessageDialog(
                    this,
                    "Beschreibung ist erforderlich.",
                    "Validierungsfehler",
                    JOptionPane.ERROR_MESSAGE
                );
                return;
            }

            // Bestimme den Typ
            KommunikationsTyp selectedType = (KommunikationsTyp) typComboBox.getSelectedItem();
            String typText = selectedType.getBezeichnung();

            // Für Sonstiges den benutzerdefinierten Text verwenden
            if (selectedType == KommunikationsTyp.SONSTIGES && !customTypField.getText().trim().isEmpty()) {
                typText = customTypField.getText().trim();
            }

            // Erstelle eine neue Kommunikation
            Kommunikation kommunikation = new Kommunikation(
                idField.getText(),
                typText,
                beschreibungField.getText(),
                notizenArea.getText()
            );

            // Füge die Kommunikation zum Kunden hinzu
            kunde.addKommunikation(kommunikation);

            // Aktualisiere die Datenbank
            boolean updated = datenbank.updateKunde(kunde);

            if (updated) {
                // Aktualisiere die Kundendetails
                displaySelectedCustomerDetails(activeList);
                kalenderPanel.aktualisieren();

                JOptionPane.showMessageDialog(
                    this,
                    "Kommunikation erfolgreich hinzugefügt.",
                    "Erfolg",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } else {
                JOptionPane.showMessageDialog(
                    this,
                    "Kommunikation konnte nicht hinzugefügt werden.",
                    "Fehler",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    /**
     * Zeigt einen Dialog zum Hinzufügen einer neuen Aktion für einen Kunden an.
     */
    private void showAddAktionDialog() {
        // Prüfe, ob ein Kunde ausgewählt ist
        JList<String> activeList = null;
        if (bestandskundenListe.getSelectedIndex() >= 0) {
            activeList = bestandskundenListe;
        } else if (interessentenListe.getSelectedIndex() >= 0) {
            activeList = interessentenListe;
        }

        if (activeList == null) {
            JOptionPane.showMessageDialog(
                this,
                "Bitte wählen Sie zuerst einen Kunden aus.",
                "Kein Kunde ausgewählt",
                JOptionPane.WARNING_MESSAGE
            );
            return;
        }

        // Hole die ID des ausgewählten Kunden
        String selectedItem = activeList.getSelectedValue();
        String id = selectedItem.split(":")[0].trim();

        // Hole den Kunden aus der Datenbank
        Kunde kunde = datenbank.getKundeById(id);
        if (kunde == null) {
            JOptionPane.showMessageDialog(
                this,
                "Der ausgewählte Kunde konnte nicht gefunden werden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }

        // Erstelle ein Panel mit einem Rasterlayout für die Formularfelder
        JPanel panel = new JPanel(new GridLayout(7, 2, 5, 5));

        // Zeige den ausgewählten Kunden an
        JLabel kundeLabel = new JLabel("Ausgewählter Kunde: " + kunde.getName());
        kundeLabel.setFont(kundeLabel.getFont().deriveFont(Font.BOLD));
        panel.add(kundeLabel);
        panel.add(new JLabel()); // Leere Zelle für das Layout

        // Erstelle Textfelder und Auswahlfelder für die Aktion
        JTextField idField = new JTextField(10);
        JComboBox<AktionsTyp> typComboBox = new JComboBox<>(AktionsTyp.values());
        JTextField titelField = new JTextField(30);
        JTextArea beschreibungArea = new JTextArea(5, 30);
        JScrollPane beschreibungScrollPane = new JScrollPane(beschreibungArea);

        // Datumsauswahl für Fälligkeitsdatum
        JSpinner datumSpinner = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor datumEditor = new JSpinner.DateEditor(datumSpinner, "dd.MM.yyyy");
        datumSpinner.setEditor(datumEditor);
        datumSpinner.setValue(new Date()); // Aktuelles Datum als Standard

        // Zeitauswahl für Fälligkeitszeit
        SpinnerDateModel zeitModel = new SpinnerDateModel();
        JSpinner zeitSpinner = new JSpinner(zeitModel);
        JSpinner.DateEditor zeitEditor = new JSpinner.DateEditor(zeitSpinner, "HH:mm");
        zeitSpinner.setEditor(zeitEditor);

        // Standardzeit: 9:00 Uhr
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 9);
        calendar.set(Calendar.MINUTE, 0);
        zeitSpinner.setValue(calendar.getTime());

        // Generiere eine eindeutige ID für die Aktion
        String newId = "AKT" + String.format("%03d", kunde.getAktionen().size() + 1);
        idField.setText(newId);
        idField.setEditable(false); // Erlaube keine Bearbeitung der ID

        // Sonstiges-Typ Handling
        JTextField customTypField = new JTextField(20);
        customTypField.setEnabled(false);

        typComboBox.addActionListener(e -> {
            AktionsTyp selectedType = (AktionsTyp) typComboBox.getSelectedItem();
            customTypField.setEnabled(selectedType == AktionsTyp.SONSTIGES);

            // Setze einen Standardtitel basierend auf dem ausgewählten Typ
            if (!titelField.getText().isEmpty() && !customTypField.isEnabled()) {
                titelField.setText(selectedType.getBezeichnung());
            }
        });

        // Setze einen Standardtitel basierend auf dem ersten Typ
        titelField.setText(AktionsTyp.values()[0].getBezeichnung());

        // Erstelle ein Panel für den Typ mit Combo-Box und Custom-Feld
        JPanel typPanel = new JPanel(new BorderLayout());
        typPanel.add(typComboBox, BorderLayout.CENTER);
        typPanel.add(customTypField, BorderLayout.EAST);

        // Füge Labels und Felder zum Panel hinzu
        panel.add(new JLabel("ID:"));
        panel.add(idField);
        panel.add(new JLabel("Typ:"));
        panel.add(typPanel);
        panel.add(new JLabel("Titel:"));
        panel.add(titelField);
        panel.add(new JLabel("Fälligkeitsdatum:"));
        panel.add(datumSpinner);
        panel.add(new JLabel("Fälligkeitszeit:"));
        panel.add(zeitSpinner);
        panel.add(new JLabel("Beschreibung:"));
        panel.add(beschreibungScrollPane);

        // Zeige den Dialog an
        int result = JOptionPane.showConfirmDialog(
            this,
            panel,
            "Neue Aktion hinzufügen",
            JOptionPane.OK_CANCEL_OPTION,
            JOptionPane.PLAIN_MESSAGE
        );

        // Verarbeite das Ergebnis
        if (result == JOptionPane.OK_OPTION) {
            // Validiere Eingabe
            if (titelField.getText().trim().isEmpty()) {
                JOptionPane.showMessageDialog(
                    this,
                    "Titel ist erforderlich.",
                    "Validierungsfehler",
                    JOptionPane.ERROR_MESSAGE
                );
                return;
            }

            // Bestimme den Titel
            String titel = titelField.getText().trim();

            // Konvertiere das Datum
            Date selectedDate = (Date) datumSpinner.getValue();
            LocalDate faelligkeitsDatum = selectedDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

            // Konvertiere die Zeit
            Date selectedTime = (Date) zeitSpinner.getValue();
            LocalTime faelligkeitsZeit = selectedTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

            // Erstelle eine neue Aktion mit Datum und Uhrzeit
            Aktion aktion = new Aktion(
                idField.getText(),
                titel,
                beschreibungArea.getText(),
                faelligkeitsDatum,
                faelligkeitsZeit
            );

            // Füge die Aktion zum Kunden hinzu
            kunde.addAktion(aktion);

            // Aktualisiere die Datenbank
            boolean updated = datenbank.updateKunde(kunde);

            if (updated) {
                // Aktualisiere die Kundendetails
                displaySelectedCustomerDetails(activeList);
                kalenderPanel.aktualisieren();

                JOptionPane.showMessageDialog(
                    this,
                    "Aktion erfolgreich hinzugefügt.",
                    "Erfolg",
                    JOptionPane.INFORMATION_MESSAGE
                );
            } else {
                JOptionPane.showMessageDialog(
                    this,
                    "Aktion konnte nicht hinzugefügt werden.",
                    "Fehler",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    /**
     * Markiert eine Aktion als erledigt und erstellt einen entsprechenden Kommunikationseintrag.
     *
     * @param kunde Der Kunde, dem die Aktion gehört
     * @param aktionId Die ID der Aktion, die als erledigt markiert werden soll
     */
    private void markiereAktionAlsErledigt(Kunde kunde, String aktionId) {
        // Suche die Aktion mit der angegebenen ID
        Aktion zuMarkierendeAktion = null;
        for (Aktion aktion : kunde.getAktionen()) {
            if (aktion.getId().equals(aktionId)) {
                zuMarkierendeAktion = aktion;
                break;
            }
        }

        if (zuMarkierendeAktion == null) {
            JOptionPane.showMessageDialog(
                this,
                "Keine Aktion mit der ID " + aktionId + " gefunden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }

        // Prüfe, ob die Aktion bereits erledigt ist
        if (zuMarkierendeAktion.isErledigt()) {
            JOptionPane.showMessageDialog(
                this,
                "Diese Aktion ist bereits als erledigt markiert.",
                "Information",
                JOptionPane.INFORMATION_MESSAGE
            );
            return;
        }

        // Markiere die Aktion als erledigt
        zuMarkierendeAktion.markiereAlsErledigt();

        // Erstelle einen Kommunikationseintrag für die erledigte Aktion
        String kommunikationsId = "KOM" + String.format("%03d", kunde.getKommunikationen().size() + 1);
        String beschreibung = "Aktion abgeschlossen: " + zuMarkierendeAktion.getTitel();
        String notizen = "Aktion mit ID " + aktionId + " wurde als erledigt markiert.";

        Kommunikation kommunikation = new Kommunikation(
            kommunikationsId,
            "Erledigte Aktion",
            beschreibung,
            notizen
        );

        kunde.addKommunikation(kommunikation);

        // Aktualisiere den Kunden in der Datenbank
        boolean updated = datenbank.updateKunde(kunde);

        if (updated) {
            // Aktualisiere die Anzeige
            JList<String> activeList = null;
            if (bestandskundenListe.getSelectedIndex() >= 0) {
                activeList = bestandskundenListe;
            } else if (interessentenListe.getSelectedIndex() >= 0) {
                activeList = interessentenListe;
            }

            if (activeList != null) {
                // Aktualisiere die Aktionen-Tabelle
                int selectedRow = aktionenTable.getSelectedRow();

                // Aktualisiere die Kundendetails
                displaySelectedCustomerDetails(activeList);

                // Versuche, die vorherige Auswahl wiederherzustellen
                if (selectedRow >= 0 && selectedRow < aktionenTable.getRowCount()) {
                    aktionenTable.setRowSelectionInterval(selectedRow, selectedRow);
                }
            }

            kalenderPanel.aktualisieren();

            JOptionPane.showMessageDialog(
                this,
                "Aktion wurde als erledigt markiert und in die Kommunikationshistorie übernommen.",
                "Erfolg",
                JOptionPane.INFORMATION_MESSAGE
            );
        } else {
            JOptionPane.showMessageDialog(
                this,
                "Fehler beim Aktualisieren des Kunden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }

    /**
     * Löscht eine Aktion eines Kunden.
     *
     * @param kunde Der Kunde, dem die Aktion gehört
     * @param aktionId Die ID der zu löschenden Aktion
     */
    private void loescheAktion(Kunde kunde, String aktionId) {
        // Suche die Aktion mit der angegebenen ID
        Aktion zuLoeschendeAktion = null;
        int aktionIndex = -1;

        for (int i = 0; i < kunde.getAktionen().size(); i++) {
            Aktion aktion = kunde.getAktionen().get(i);
            if (aktion.getId().equals(aktionId)) {
                zuLoeschendeAktion = aktion;
                aktionIndex = i;
                break;
            }
        }

        if (zuLoeschendeAktion == null) {
            JOptionPane.showMessageDialog(
                this,
                "Keine Aktion mit der ID " + aktionId + " gefunden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
            return;
        }

        // Bestätigungsdialog anzeigen
        int confirm = JOptionPane.showConfirmDialog(
            this,
            "Sind Sie sicher, dass Sie die Aktion '" + zuLoeschendeAktion.getTitel() + "' löschen möchten?",
            "Aktion löschen",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        );

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        // Entferne die Aktion aus der Liste
        kunde.getAktionen().remove(aktionIndex);

        // Aktualisiere den Kunden in der Datenbank
        boolean updated = datenbank.updateKunde(kunde);

        if (updated) {
            // Aktualisiere die Anzeige
            JList<String> activeList = null;
            if (bestandskundenListe.getSelectedIndex() >= 0) {
                activeList = bestandskundenListe;
            } else if (interessentenListe.getSelectedIndex() >= 0) {
                activeList = interessentenListe;
            }

            if (activeList != null) {
                // Aktualisiere die Kundendetails
                displaySelectedCustomerDetails(activeList);
            }

            kalenderPanel.aktualisieren();

            JOptionPane.showMessageDialog(
                this,
                "Aktion wurde erfolgreich gelöscht.",
                "Erfolg",
                JOptionPane.INFORMATION_MESSAGE
            );
        } else {
            JOptionPane.showMessageDialog(
                this,
                "Fehler beim Aktualisieren des Kunden.",
                "Fehler",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }

    /**
     * Validiert die Eingabefelder für einen Kunden und markiert fehlende Felder rot.
     *
     * @param vornameField Das Textfeld für den Vornamen
     * @param nachnameField Das Textfeld für den Nachnamen
     * @param addressField Das Textfeld für die Adresse
     * @param phoneField Das Textfeld für die Telefonnummer
     * @param emailField Das Textfeld für die E-Mail-Adresse
     * @return Eine Fehlermeldung, wenn ein Feld leer ist, sonst null
     */
    private String validateCustomerFields(JTextField vornameField, JTextField nachnameField, JTextField addressField, JTextField phoneField, JTextField emailField) {
        boolean isValid = true;
        StringBuilder errorMessage = new StringBuilder("Bitte füllen Sie die folgenden Felder aus:\n");

        // Setze alle Felder zurück auf normale Farbe
        vornameField.setBorder(BorderFactory.createLineBorder(Color.GRAY));
        nachnameField.setBorder(BorderFactory.createLineBorder(Color.GRAY));
        addressField.setBorder(BorderFactory.createLineBorder(Color.GRAY));
        phoneField.setBorder(BorderFactory.createLineBorder(Color.GRAY));
        emailField.setBorder(BorderFactory.createLineBorder(Color.GRAY));

        if (vornameField.getText() == null || vornameField.getText().trim().isEmpty()) {
            vornameField.setBorder(BorderFactory.createLineBorder(Color.RED, 2));
            errorMessage.append("- Vorname\n");
            isValid = false;
        }

        if (nachnameField.getText() == null || nachnameField.getText().trim().isEmpty()) {
            nachnameField.setBorder(BorderFactory.createLineBorder(Color.RED, 2));
            errorMessage.append("- Nachname\n");
            isValid = false;
        }

        if (addressField.getText() == null || addressField.getText().trim().isEmpty()) {
            addressField.setBorder(BorderFactory.createLineBorder(Color.RED, 2));
            errorMessage.append("- Adresse\n");
            isValid = false;
        }

        if (phoneField.getText() == null || phoneField.getText().trim().isEmpty()) {
            phoneField.setBorder(BorderFactory.createLineBorder(Color.RED, 2));
            errorMessage.append("- Telefonnummer\n");
            isValid = false;
        }

        if (emailField.getText() == null || emailField.getText().trim().isEmpty()) {
            emailField.setBorder(BorderFactory.createLineBorder(Color.RED, 2));
            errorMessage.append("- E-Mail-Adresse\n");
            isValid = false;
        }

        return isValid ? null : errorMessage.toString();
    }

    /**
     * Erstellt einen stilisierten Button mit modernem Aussehen.
     * 
     * @param text Der Text des Buttons
     * @param backgroundColor Die Hintergrundfarbe des Buttons
     * @return Ein stilisierter JButton
     */
    private JButton createStyledButton(String text, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(backgroundColor.darker(), 1, true),
            BorderFactory.createEmptyBorder(8, 15, 8, 15)
        ));

        // Füge einen Hover-Effekt hinzu
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(backgroundColor.darker());
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(backgroundColor);
            }
        });

        return button;
    }

    /**
     * Zeigt einen Dialog mit Informationen über das CRM-System an.
     */
    private void showAboutDialog() {
        StringBuilder aboutText = new StringBuilder();
        aboutText.append("CRM-System - Funktionsübersicht\n\n");
        aboutText.append("Dieses CRM-System bietet folgende Funktionen:\n\n");

        aboutText.append("1. Kundenverwaltung\n");
        aboutText.append("   - Bestandskunden und Interessenten verwalten\n");
        aboutText.append("   - Kunden hinzufügen, bearbeiten und löschen\n");
        aboutText.append("   - Detaillierte Kundeninformationen anzeigen\n\n");

        aboutText.append("2. Kommunikationsverfolgung\n");
        aboutText.append("   - Kommunikationen mit Kunden erfassen\n");
        aboutText.append("   - Verschiedene Kommunikationstypen (Anruf, E-Mail, Meeting, etc.)\n\n");

        aboutText.append("3. Aktionen- und Aufgabenverwaltung\n");
        aboutText.append("   - Aktionen für Kunden planen\n");
        aboutText.append("   - Aktionen als erledigt markieren oder löschen\n");
        aboutText.append("   - Benachrichtigungen für anstehende und überfällige Aktionen\n\n");

        aboutText.append("4. Kalender\n");
        aboutText.append("   - Übersicht aller geplanten Aktionen\n");
        aboutText.append("   - Tagesansicht mit detaillierten Informationen\n\n");

        aboutText.append("5. Statistik und Berichte\n");
        aboutText.append("   - Auswertungen zu Kunden und Aktivitäten\n");
        aboutText.append("   - Export- und Druckfunktionen\n\n");

        aboutText.append("6. Suchfunktion\n");
        aboutText.append("   - Schnelle Suche nach Kunden\n\n");

        aboutText.append("© 2025 CRM-System");

        JOptionPane.showMessageDialog(
            this,
            aboutText.toString(),
            "Über das CRM-System",
            JOptionPane.INFORMATION_MESSAGE
        );
    }

    /**
     * Hauptmethode zum Starten der GUI-Anwendung.
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            CrmGui gui = new CrmGui();
            gui.setVisible(true);
        });
    }
}
