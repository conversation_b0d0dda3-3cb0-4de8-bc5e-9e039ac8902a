package gui;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.text.DecimalFormat;

/**
 * Eine wiederverwendbare Komponente für die Darstellung von Statistik-Karten.
 */
public class StatistikKartePanel extends JPanel {
    private final JLabel titelLabel;
    private final JLabel wertLabel;
    private final JLabel beschreibungLabel;
    private final JPanel contentPanel;
    private final Color hintergrundFarbe;
    private final Color akzentFarbe;

    /**
     * Konstruktor für eine Statistik-Karte.
     *
     * @param titel Der Titel der Karte
     * @param wert Der anzuzeigende Wert
     * @param beschreibung Eine optionale Beschreibung
     * @param hintergrundFarbe Die Hintergrundfarbe der Karte
     * @param akzentFarbe Die Akzentfarbe für den Titel
     */
    public StatistikKartePanel(String titel, String wert, String beschreibung, Color hintergrundFarbe, Color akzentFarbe) {
        this.hintergrundFarbe = hintergrundFarbe;
        this.akzentFarbe = akzentFarbe;
        
        // Setze Layout und Rahmen
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(akzentFarbe, 2),
            new EmptyBorder(10, 10, 10, 10)
        ));
        setBackground(hintergrundFarbe);
        
        // Erstelle die Labels
        titelLabel = new JLabel(titel);
        titelLabel.setFont(new Font("Arial", Font.BOLD, 14));
        titelLabel.setForeground(akzentFarbe);
        
        wertLabel = new JLabel(wert);
        wertLabel.setFont(new Font("Arial", Font.BOLD, 24));
        wertLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        beschreibungLabel = new JLabel(beschreibung);
        beschreibungLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        beschreibungLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        // Erstelle das Content-Panel
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(hintergrundFarbe);
        contentPanel.add(wertLabel, BorderLayout.CENTER);
        contentPanel.add(beschreibungLabel, BorderLayout.SOUTH);
        
        // Füge die Komponenten zum Panel hinzu
        add(titelLabel, BorderLayout.NORTH);
        add(contentPanel, BorderLayout.CENTER);
        
        // Setze die bevorzugte Größe
        setPreferredSize(new Dimension(200, 150));
    }

    /**
     * Konstruktor für eine Statistik-Karte mit Standardfarben.
     *
     * @param titel Der Titel der Karte
     * @param wert Der anzuzeigende Wert
     * @param beschreibung Eine optionale Beschreibung
     */
    public StatistikKartePanel(String titel, String wert, String beschreibung) {
        this(titel, wert, beschreibung, Color.WHITE, new Color(70, 130, 180)); // Standardfarben: Weiß und Stahlblau
    }

    /**
     * Konstruktor für eine Statistik-Karte mit einem numerischen Wert.
     *
     * @param titel Der Titel der Karte
     * @param wert Der anzuzeigende numerische Wert
     * @param beschreibung Eine optionale Beschreibung
     * @param hintergrundFarbe Die Hintergrundfarbe der Karte
     * @param akzentFarbe Die Akzentfarbe für den Titel
     */
    public StatistikKartePanel(String titel, int wert, String beschreibung, Color hintergrundFarbe, Color akzentFarbe) {
        this(titel, String.valueOf(wert), beschreibung, hintergrundFarbe, akzentFarbe);
    }

    /**
     * Konstruktor für eine Statistik-Karte mit einem numerischen Wert und Standardfarben.
     *
     * @param titel Der Titel der Karte
     * @param wert Der anzuzeigende numerische Wert
     * @param beschreibung Eine optionale Beschreibung
     */
    public StatistikKartePanel(String titel, int wert, String beschreibung) {
        this(titel, String.valueOf(wert), beschreibung);
    }

    /**
     * Konstruktor für eine Statistik-Karte mit einem Prozentwert.
     *
     * @param titel Der Titel der Karte
     * @param prozent Der anzuzeigende Prozentwert
     * @param beschreibung Eine optionale Beschreibung
     * @param hintergrundFarbe Die Hintergrundfarbe der Karte
     * @param akzentFarbe Die Akzentfarbe für den Titel
     */
    public StatistikKartePanel(String titel, double prozent, String beschreibung, Color hintergrundFarbe, Color akzentFarbe) {
        this(titel, new DecimalFormat("0.0").format(prozent) + "%", beschreibung, hintergrundFarbe, akzentFarbe);
    }

    /**
     * Konstruktor für eine Statistik-Karte mit einem Prozentwert und Standardfarben.
     *
     * @param titel Der Titel der Karte
     * @param prozent Der anzuzeigende Prozentwert
     * @param beschreibung Eine optionale Beschreibung
     */
    public StatistikKartePanel(String titel, double prozent, String beschreibung) {
        this(titel, new DecimalFormat("0.0").format(prozent) + "%", beschreibung);
    }

    /**
     * Aktualisiert den Wert der Karte.
     *
     * @param wert Der neue Wert
     */
    public void setWert(String wert) {
        wertLabel.setText(wert);
    }

    /**
     * Aktualisiert den Wert der Karte mit einem numerischen Wert.
     *
     * @param wert Der neue numerische Wert
     */
    public void setWert(int wert) {
        wertLabel.setText(String.valueOf(wert));
    }

    /**
     * Aktualisiert den Wert der Karte mit einem Prozentwert.
     *
     * @param prozent Der neue Prozentwert
     */
    public void setWert(double prozent) {
        wertLabel.setText(new DecimalFormat("0.0").format(prozent) + "%");
    }

    /**
     * Aktualisiert die Beschreibung der Karte.
     *
     * @param beschreibung Die neue Beschreibung
     */
    public void setBeschreibung(String beschreibung) {
        beschreibungLabel.setText(beschreibung);
    }
}
