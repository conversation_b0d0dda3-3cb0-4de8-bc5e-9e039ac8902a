package persistence;

import model.NutzerDatenbank;

/**
 * Interface für Persistierung-Manager im CRM-System.
 * Definiert die grundlegenden Operationen zum Speichern und Laden von Daten.
 */
public interface PersistenceManager {
    
    /**
     * Speichert die komplette Kundendatenbank.
     *
     * @param datenbank Die zu speichernde Kundendatenbank
     * @throws PersistenceException Wenn beim Speichern ein Fehler auftritt
     */
    void speichern(NutzerDatenbank datenbank) throws PersistenceException;
    
    /**
     * Lädt die Kundendatenbank aus dem persistenten Speicher.
     *
     * @return Die geladene Kundendatenbank oder eine neue leere Datenbank, wenn keine Daten vorhanden sind
     * @throws PersistenceException Wenn beim Laden ein Fehler auftritt
     */
    NutzerDatenbank laden() throws PersistenceException;
    
    /**
     * Prüft, ob gespeicherte Daten vorhanden sind.
     *
     * @return true, wenn Daten vorhanden sind, false sonst
     */
    boolean sindDatenVorhanden();
    
    /**
     * Erstellt ein Backup der aktuellen Daten.
     *
     * @param datenbank Die zu sichernde Kundendatenbank
     * @throws PersistenceException Wenn beim Backup ein Fehler auftritt
     */
    void erstelleBackup(NutzerDatenbank datenbank) throws PersistenceException;
}
