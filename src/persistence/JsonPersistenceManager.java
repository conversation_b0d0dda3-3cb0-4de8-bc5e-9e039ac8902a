package persistence;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import model.*;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * JSON-basierte Implementierung des PersistenceManager.
 * Speichert und lädt Kundendaten im JSON-Format.
 */
public class JsonPersistenceManager implements PersistenceManager {
    
    private static final String DATA_FILE = "crm_data.json";
    private static final String BACKUP_DIR = "backups";
    private static final DateTimeFormatter BACKUP_TIMESTAMP = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    
    private final Gson gson;
    private final Path dataFilePath;
    private final Path backupDirPath;
    
    /**
     * Konstruktor für den JSON-Persistierung-Manager.
     */
    public JsonPersistenceManager() {
        this.dataFilePath = Paths.get(DATA_FILE);
        this.backupDirPath = Paths.get(BACKUP_DIR);
        
        // Konfiguriere Gson mit benutzerdefinierten Adaptern für LocalDateTime
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .create();
        
        // Erstelle Backup-Verzeichnis falls es nicht existiert
        erstelleBackupVerzeichnis();
    }
    
    @Override
    public void speichern(NutzerDatenbank datenbank) throws PersistenceException {
        try {
            // Konvertiere NutzerDatenbank zu JSON-kompatiblem Format
            CrmData crmData = new CrmData();
            crmData.kunden = datenbank.getAlleKunden();
            
            // Schreibe JSON-Datei
            try (FileWriter writer = new FileWriter(dataFilePath.toFile())) {
                gson.toJson(crmData, writer);
            }
            
        } catch (IOException e) {
            throw new PersistenceException("Fehler beim Speichern der Daten: " + e.getMessage(), e);
        }
    }
    
    @Override
    public NutzerDatenbank laden() throws PersistenceException {
        if (!sindDatenVorhanden()) {
            return new NutzerDatenbank(); // Neue leere Datenbank
        }
        
        try {
            // Lese JSON-Datei
            try (FileReader reader = new FileReader(dataFilePath.toFile())) {
                CrmData crmData = gson.fromJson(reader, CrmData.class);
                
                // Erstelle neue NutzerDatenbank und füge Kunden hinzu
                NutzerDatenbank datenbank = new NutzerDatenbank();
                if (crmData != null && crmData.kunden != null) {
                    for (Kunde kunde : crmData.kunden) {
                        datenbank.addKunde(kunde);
                    }
                }
                
                return datenbank;
            }
            
        } catch (IOException | JsonSyntaxException e) {
            throw new PersistenceException("Fehler beim Laden der Daten: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean sindDatenVorhanden() {
        return Files.exists(dataFilePath) && dataFilePath.toFile().length() > 0;
    }
    
    @Override
    public void erstelleBackup(NutzerDatenbank datenbank) throws PersistenceException {
        try {
            // Erstelle Backup-Dateiname mit Zeitstempel
            String timestamp = LocalDateTime.now().format(BACKUP_TIMESTAMP);
            String backupFileName = "crm_backup_" + timestamp + ".json";
            Path backupFilePath = backupDirPath.resolve(backupFileName);
            
            // Speichere Backup
            CrmData crmData = new CrmData();
            crmData.kunden = datenbank.getAlleKunden();
            
            try (FileWriter writer = new FileWriter(backupFilePath.toFile())) {
                gson.toJson(crmData, writer);
            }
            
        } catch (IOException e) {
            throw new PersistenceException("Fehler beim Erstellen des Backups: " + e.getMessage(), e);
        }
    }
    
    /**
     * Erstellt das Backup-Verzeichnis falls es nicht existiert.
     */
    private void erstelleBackupVerzeichnis() {
        try {
            if (!Files.exists(backupDirPath)) {
                Files.createDirectories(backupDirPath);
            }
        } catch (IOException e) {
            System.err.println("Warnung: Backup-Verzeichnis konnte nicht erstellt werden: " + e.getMessage());
        }
    }
    
    /**
     * Hilfklasse für JSON-Serialisierung der CRM-Daten.
     */
    private static class CrmData {
        List<Kunde> kunden;
    }
    
    /**
     * Gson-Adapter für LocalDateTime-Serialisierung.
     */
    private static class LocalDateTimeAdapter implements JsonSerializer<LocalDateTime>, JsonDeserializer<LocalDateTime> {
        private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        
        @Override
        public JsonElement serialize(LocalDateTime localDateTime, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(localDateTime.format(formatter));
        }
        
        @Override
        public LocalDateTime deserialize(JsonElement json, Type type, JsonDeserializationContext context) 
                throws JsonParseException {
            return LocalDateTime.parse(json.getAsString(), formatter);
        }
    }
}
