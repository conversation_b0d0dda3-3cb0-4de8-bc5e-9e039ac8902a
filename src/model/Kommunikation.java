package model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Klasse welche die Kommunikation mit einem Kunden darstellt.
 */
public class Kommunikation {
    private String id;
    private LocalDateTime datum;
    private String typ;        // z.B. "E-Mail", "Telefon", "Meeting"
    private String beschreibung;
    private String notizen;

    /**
     * Konstruktor zum Erstellen eines neuen Kommunikationseintrags.
     *
     * @param id Eindeutige ID für die Kommunikation
     * @param typ Typ der Kommunikation (z.B. E-Mail, Telefon)
     * @param beschreibung Kurzbeschreibung der Kommunikation
     * @param notizen Detaillierte Notizen zur Kommunikation
     */
    public Kommunikation(String id, String typ, String beschreibung, String notizen) {
        this.id = id;
        this.datum = LocalDateTime.now();
        this.typ = typ;
        this.beschreibung = beschreibung;
        this.notizen = notizen;
    }

    // Getter und Setter
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getDatum() {
        return datum;
    }

    public void setDatum(LocalDateTime datum) {
        this.datum = datum;
    }

    public String getTyp() {
        return typ;
    }

    public void setTyp(String typ) {
        this.typ = typ;
    }

    public String getBeschreibung() {
        return beschreibung;
    }

    public void setBeschreibung(String beschreibung) {
        this.beschreibung = beschreibung;
    }

    public String getNotizen() {
        return notizen;
    }

    public void setNotizen(String notizen) {
        this.notizen = notizen;
    }

    /**
     * Gibt das Datum in einem lesbaren Format zurück.
     *
     * @return Das formatierte Datum (z.B. "Am 16.05. um 15:30")
     */
    public String getFormattiertesDatum() {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd.MM.");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

        return "Am " + datum.format(dateFormatter) + " um " + datum.format(timeFormatter);
    }

    @Override
    public String toString() {
        return "Kommunikation{" +
                "id='" + id + '\'' +
                ", datum=" + datum +
                ", typ='" + typ + '\'' +
                ", beschreibung='" + beschreibung + '\'' +
                '}';
    }
}
