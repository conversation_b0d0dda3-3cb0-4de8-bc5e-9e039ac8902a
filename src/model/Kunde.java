package model;

import java.util.ArrayList;
import java.util.List;

/**
 * Hauptklasse zur Darstellung eines Kunden im CRM-System.
 */
public class Kunde {
    private String id;
    private String name;
    private String adresse;
    private String telefonnummer;
    private String email;
    private Kundenkategorie kategorie;
    private List<Kommunikation> kommunikationen;
    private List<Aktion> aktionen;

    /**
     * Konstruktor zum Erstellen eines neuen Kunden.
     *
     * @param id Eindeutige ID für den Kunden
     * @param name Name des Kunden, z.B. "<PERSON> Mustermann"
     * @param adresse Adresse des Kunden, z.B. "Musterstraße 123, 12345 Berlin"
     * @param telefonnummer Telefonnummer des Kunden, z.B. "+49 123 456789"
     * @param email E-Mail-Adresse des Kunden, z.B. "<EMAIL>"
     * @param kategorie Kundenkategorie (BESTANDSKUNDE oder INTERESSENT)
     */
    public Kunde(String id, String name, String adresse, String telefonnummer,
                String email, Kundenkategorie kategorie) {
        this.id = id;
        this.name = name;
        this.adresse = adresse;
        this.telefonnummer = telefonnummer;
        this.email = email;
        this.kategorie = kategorie;
        this.kommunikationen = new ArrayList<>();
        this.aktionen = new ArrayList<>();
    }

    // Getter und Setter
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAdresse() {
        return adresse;
    }

    public void setAdresse(String adresse) {
        this.adresse = adresse;
    }

    public String getTelefonnummer() {
        return telefonnummer;
    }

    public void setTelefonnummer(String telefonnummer) {
        this.telefonnummer = telefonnummer;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Kundenkategorie getKategorie() {
        return kategorie;
    }

    public void setKategorie(Kundenkategorie kategorie) {
        this.kategorie = kategorie;
    }

    public List<Kommunikation> getKommunikationen() {
        return kommunikationen;
    }

    public List<Aktion> getAktionen() {
        return aktionen;
    }

    /**
     * Fügt einen neuen Kommunikationseintrag für diesen Kunden hinzu.
     *
     * @param kommunikation Der hinzuzufügende Kommunikationseintrag
     */
    public void addKommunikation(Kommunikation kommunikation) {
        this.kommunikationen.add(kommunikation);
    }

    /**
     * Fügt eine neue Aktion/Aufgabe für diesen Kunden hinzu.
     *
     * @param aktion Die hinzuzufügende Aktion
     */
    public void addAktion(Aktion aktion) {
        this.aktionen.add(aktion);
    }

    @Override
    public String toString() {
        return "Kunde{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", adresse='" + adresse + '\'' +
                ", telefonnummer='" + telefonnummer + '\'' +
                ", email='" + email + '\'' +
                ", kategorie=" + kategorie +
                ", anzahlKommunikationen=" + kommunikationen.size() +
                ", anzahlAktionen=" + aktionen.size() +
                '}';
    }
}
