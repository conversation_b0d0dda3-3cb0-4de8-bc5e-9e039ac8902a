package model;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Klasse zur Verwaltung einer Datenbank von Kunden (Nutzern) im CRM-System.
 * Stellt Methoden zum Hinzufügen, Abrufen und Filtern von Kunden bereit.
 */
public class NutzerDatenbank {
    // Verwendung sowohl einer Liste als auch einer Map für effiziente Operationen
    private final List<Kunde> kundenListe;
    private final Map<String, Kunde> kundenMap; // Ordnet ID dem Kunden zu

    /**
     * Konstruktor zum Erstellen einer neuen Kundendatenbank.
     */
    public NutzerDatenbank() {
        this.kundenListe = new ArrayList<>();
        this.kundenMap = new HashMap<>();
    }

    /**
     * <PERSON>ügt einen neuen Kunden zur Datenbank hinzu.
     *
     * @param kunde Der hinzuzufügende Kunde
     * @return true, wenn erfolgreich hinzugefügt, false, wenn bereits ein Kunde mit derselben ID existiert
     */
    public boolean addKunde(Kunde kunde) {
        if (kundenMap.containsKey(kunde.getId())) {
            return false; // Kunde mit dieser ID existiert bereits
        }

        kundenListe.add(kunde);
        kundenMap.put(kunde.getId(), kunde);
        return true;
    }

    /**
     * Ruft einen Kunden anhand seiner ID ab.
     *
     * @param id Die ID des abzurufenden Kunden
     * @return Der Kunde mit der angegebenen ID oder null, wenn nicht gefunden
     */
    public Kunde getKundeById(String id) {
        return kundenMap.get(id);
    }

    /**
     * Ruft alle Kunden in der Datenbank ab.
     *
     * @return Eine Liste aller Kunden
     */
    public List<Kunde> getAlleKunden() {
        return new ArrayList<>(kundenListe);
    }

    /**
     * Aktualisiert einen vorhandenen Kunden in der Datenbank.
     *
     * @param kunde Die aktualisierten Kundeninformationen
     * @return true, wenn erfolgreich aktualisiert, false, wenn der Kunde nicht gefunden wurde
     */
    public boolean updateKunde(Kunde kunde) {
        if (!kundenMap.containsKey(kunde.getId())) {
            return false; // Kunde nicht gefunden
        }

        // Finde den Index des Kunden in der Liste
        int index = -1;
        for (int i = 0; i < kundenListe.size(); i++) {
            if (kundenListe.get(i).getId().equals(kunde.getId())) {
                index = i;
                break;
            }
        }

        if (index != -1) {
            kundenListe.set(index, kunde);
            kundenMap.put(kunde.getId(), kunde);
            return true;
        }

        return false;
    }

    /**
     * Entfernt einen Kunden aus der Datenbank.
     *
     * @param id Die ID des zu entfernenden Kunden
     * @return true, wenn erfolgreich entfernt, false, wenn der Kunde nicht gefunden wurde
     */
    public boolean removeKunde(String id) {
        Kunde kunde = kundenMap.get(id);
        if (kunde == null) {
            return false; // Kunde nicht gefunden
        }

        kundenListe.remove(kunde);
        kundenMap.remove(id);
        return true;
    }

    /**
     * Filtert Kunden nach Namen (Groß-/Kleinschreibung wird ignoriert, Teilabgleich).
     *
     * @param namePart Der Name oder Teil des Namens, nach dem gesucht werden soll
     * @return Eine Liste von Kunden, deren Namen den angegebenen String enthalten
     */
    public List<Kunde> filterByName(String namePart) {
        if (namePart == null || namePart.trim().isEmpty()) {
            return new ArrayList<>(kundenListe);
        }

        String searchTerm = namePart.toLowerCase().trim();

        return kundenListe.stream()
                .filter(kunde -> kunde.getName().toLowerCase().contains(searchTerm))
                .collect(Collectors.toList());
    }

    /**
     * Filtert Kunden nach Kategorie.
     *
     * @param kategorie Die Kategorie, nach der gefiltert werden soll
     * @return Eine Liste von Kunden in der angegebenen Kategorie
     */
    public List<Kunde> filterByKategorie(Kundenkategorie kategorie) {
        if (kategorie == null) {
            return new ArrayList<>(kundenListe);
        }

        return kundenListe.stream()
                .filter(kunde -> kunde.getKategorie() == kategorie)
                .collect(Collectors.toList());
    }

    /**
     * Gibt die Anzahl der Kunden in der Datenbank zurück.
     *
     * @return Die Anzahl der Kunden
     */
    public int getAnzahlKunden() {
        return kundenListe.size();
    }

    /**
     * Holt alle anstehenden Aktionen von allen Kunden.
     *
     * @return Eine Liste aller Aktionen, sortiert nach Fälligkeitsdatum
     */
    public List<AktionMitKunde> getAlleAktionen() {
        List<AktionMitKunde> alleAktionen = new ArrayList<>();

        for (Kunde kunde : kundenListe) {
            for (Aktion aktion : kunde.getAktionen()) {
                alleAktionen.add(new AktionMitKunde(aktion, kunde));
            }
        }

        // Sortiere nach Fälligkeitsdatum (aufsteigend)
        alleAktionen.sort(Comparator.comparing(a -> a.getAktion().getFaelligkeitsDatum()));

        return alleAktionen;
    }

    /**
     * Zählt die Anzahl der heute fälligen Aktionen.
     *
     * @return Die Anzahl der heute fälligen Aktionen
     */
    public int getAnzahlHeuteFaelligeAktionen() {
        return (int) getAlleAktionen().stream()
                .filter(AktionMitKunde::istHeuteFaellig)
                .count();
    }

    /**
     * Zählt die Anzahl der überfälligen Aktionen.
     *
     * @return Die Anzahl der überfälligen Aktionen
     */
    public int getAnzahlUeberfaelligeAktionen() {
        return (int) getAlleAktionen().stream()
                .filter(AktionMitKunde::istUeberfaellig)
                .count();
    }

    /**
     * Hilfsklasse, um eine Aktion mit dem zugehörigen Kunden zu verknüpfen.
     */
    public static class AktionMitKunde {
        private final Aktion aktion;
        private final Kunde kunde;

        public AktionMitKunde(Aktion aktion, Kunde kunde) {
            this.aktion = aktion;
            this.kunde = kunde;
        }

        public Aktion getAktion() {
            return aktion;
        }

        public Kunde getKunde() {
            return kunde;
        }

        /**
         * Prüft, ob die Aktion überfällig ist.
         *
         * @return true, wenn das Fälligkeitsdatum vor dem heutigen Datum liegt und die Aktion nicht erledigt ist
         */
        public boolean istUeberfaellig() {
            return !aktion.isErledigt() && aktion.getFaelligkeitsDatum().isBefore(LocalDate.now());
        }

        /**
         * Prüft, ob die Aktion heute fällig ist.
         *
         * @return true, wenn das Fälligkeitsdatum heute ist und die Aktion nicht erledigt ist
         */
        public boolean istHeuteFaellig() {
            return !aktion.isErledigt() && aktion.getFaelligkeitsDatum().isEqual(LocalDate.now());
        }
    }
}
