package model;

/**
 * Enum für verschiedene Kommunikationstypen im CRM-System.
 */
public enum KommunikationsTyp {
    EMAIL("E-Mail"),
    ANRUF("Anruf"),
    KUNDENGESPRAECH("Kundengespräch"),
    GARANTIEFALL("Garantiefall"),
    SONSTIGES("Sonstiges");

    private final String bezeichnung;

    /**
     * Konstruktor für KommunikationsTyp.
     *
     * @param bezeichnung Die Bezeichnung des Kommunikationstyps
     */
    KommunikationsTyp(String bezeichnung) {
        this.bezeichnung = bezeichnung;
    }

    /**
     * Gibt die Bezeichnung des Kommunikationstyps zurück.
     *
     * @return Die Bezeichnung des Kommunikationstyps
     */
    public String getBezeichnung() {
        return bezeichnung;
    }

    @Override
    public String toString() {
        return bezeichnung;
    }
}
