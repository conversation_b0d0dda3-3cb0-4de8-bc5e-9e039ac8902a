package model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Klasse welche eine Aktion oder Aufgabe im Zusammenhang mit einem Kunden darstellt.
 */
public class Aktion {
    private String id;
    private String titel;
    private String beschreibung;
    private LocalDateTime faelligkeitsDatumZeit;
    private boolean erledigt;

    /**
     * Konstruktor zum Erstellen einer neuen Aktion/Aufgabe.
     *
     * @param id Eindeutige ID für die Aktion
     * @param titel Titel der Aktion
     * @param beschreibung Beschreibung der Aktion
     * @param faelligkeitsDatum Fälligkeitsdatum der Aktion
     */
    public Aktion(String id, String titel, String beschreibung, LocalDate faelligkeitsDatum) {
        this(id, titel, beschreibung, faelligkeitsDatum, LocalTime.of(9, 0)); // Standardzeit: 9:00 Uhr
    }

    /**
     * Konstruktor zum Erstellen einer neuen Aktion/Aufgabe mit Datum und Uhrzeit.
     *
     * @param id Eindeutige ID für die Aktion
     * @param titel Titel der Aktion
     * @param beschreibung Beschreibung der Aktion
     * @param faelligkeitsDatum Fälligkeitsdatum der Aktion
     * @param faelligkeitsZeit Fälligkeitszeit der Aktion
     */
    public Aktion(String id, String titel, String beschreibung, LocalDate faelligkeitsDatum, LocalTime faelligkeitsZeit) {
        this.id = id;
        this.titel = titel;
        this.beschreibung = beschreibung;
        this.faelligkeitsDatumZeit = LocalDateTime.of(faelligkeitsDatum, faelligkeitsZeit);
        this.erledigt = false;
    }

    // Getter und Setter
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitel() {
        return titel;
    }

    public void setTitel(String titel) {
        this.titel = titel;
    }

    public String getBeschreibung() {
        return beschreibung;
    }

    public void setBeschreibung(String beschreibung) {
        this.beschreibung = beschreibung;
    }

    public LocalDateTime getFaelligkeitsDatumZeit() {
        return faelligkeitsDatumZeit;
    }

    public void setFaelligkeitsDatumZeit(LocalDateTime faelligkeitsDatumZeit) {
        this.faelligkeitsDatumZeit = faelligkeitsDatumZeit;
    }

    /**
     * Gibt das Fälligkeitsdatum ohne Uhrzeit zurück.
     *
     * @return Das Fälligkeitsdatum
     */
    public LocalDate getFaelligkeitsDatum() {
        return faelligkeitsDatumZeit.toLocalDate();
    }

    /**
     * Setzt das Fälligkeitsdatum, behält aber die Uhrzeit bei.
     *
     * @param faelligkeitsDatum Das neue Fälligkeitsdatum
     */
    public void setFaelligkeitsDatum(LocalDate faelligkeitsDatum) {
        this.faelligkeitsDatumZeit = LocalDateTime.of(faelligkeitsDatum, getFaelligkeitsZeit());
    }

    /**
     * Gibt die Fälligkeitszeit ohne Datum zurück.
     *
     * @return Die Fälligkeitszeit
     */
    public LocalTime getFaelligkeitsZeit() {
        return faelligkeitsDatumZeit.toLocalTime();
    }

    /**
     * Setzt die Fälligkeitszeit, behält aber das Datum bei.
     *
     * @param faelligkeitsZeit Die neue Fälligkeitszeit
     */
    public void setFaelligkeitsZeit(LocalTime faelligkeitsZeit) {
        this.faelligkeitsDatumZeit = LocalDateTime.of(getFaelligkeitsDatum(), faelligkeitsZeit);
    }

    public boolean isErledigt() {
        return erledigt;
    }

    public void setErledigt(boolean erledigt) {
        this.erledigt = erledigt;
    }

    /**
     * Markiert die Aktion als erledigt.
     */
    public void markiereAlsErledigt() {
        this.erledigt = true;
    }

    @Override
    public String toString() {
        return "Aktion{" +
                "id='" + id + '\'' +
                ", titel='" + titel + '\'' +
                ", faelligkeitsDatumZeit=" + faelligkeitsDatumZeit +
                ", erledigt=" + erledigt +
                '}';
    }
}
