package model;

/**
 * Enum für Kundenkategorien im CRM-System.
 * Unterscheidet zwischen bestehenden Kunden und potenziellen Kunden (Interessenten).
 */
public enum Kundenkategorie {
    BESTANDSKUNDE("Bestandskunde"),    // Bereits ein Kunde
    INTERESSENT("Interessent");       // Potenzieller Kunde

    private final String bezeichnung;

    /**
     * Konstruktor für Kundenkategorie.
     *
     * @param bezeichnung Die Bezeichnung der Kategorie
     */
    Kundenkategorie(String bezeichnung) {
        this.bezeichnung = bezeichnung;
    }

    /**
     * Gibt die Bezeichnung der Kundenkategorie zurück.
     *
     * @return Die Bezeichnung der Kategorie
     */
    public String getBezeichnung() {
        return bezeichnung;
    }

    @Override
    public String toString() {
        return bezeichnung;
    }
}
