package model;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Hilfsklasse zur Berechnung von Statistiken aus der Kundendatenbank.
 */
public class StatistikDaten {
    private final NutzerDatenbank datenbank;

    /**
     * Konstruktor für die StatistikDaten-Klasse.
     *
     * @param datenbank Die Kundendatenbank
     */
    public StatistikDaten(NutzerDatenbank datenbank) {
        this.datenbank = datenbank;
    }

    /**
     * Gibt die Anzahl der Kunden nach Kategorie zurück.
     *
     * @return Eine Map mit der Kundenkategorie als Schlüssel und der Anzahl als Wert
     */
    public Map<Kundenkategorie, Integer> getKundenNachKategorie() {
        Map<Kundenkategorie, Integer> result = new HashMap<>();
        
        // Initialisiere alle Kategorien mit 0
        for (Kundenkategorie kategorie : Kundenkategorie.values()) {
            result.put(kategorie, 0);
        }
        
        // Zähle die Kunden pro Kategorie
        for (Kunde kunde : datenbank.getAlleKunden()) {
            Kundenkategorie kategorie = kunde.getKategorie();
            result.put(kategorie, result.get(kategorie) + 1);
        }
        
        return result;
    }

    /**
     * Gibt die Anzahl der Aktionen nach Status zurück.
     *
     * @return Eine Map mit dem Status als Schlüssel und der Anzahl als Wert
     */
    public Map<String, Integer> getAktionenNachStatus() {
        Map<String, Integer> result = new HashMap<>();
        result.put("Überfällig", 0);
        result.put("Heute fällig", 0);
        result.put("Anstehend", 0);
        result.put("Erledigt", 0);
        
        List<NutzerDatenbank.AktionMitKunde> alleAktionen = datenbank.getAlleAktionen();
        
        for (NutzerDatenbank.AktionMitKunde aktionMitKunde : alleAktionen) {
            Aktion aktion = aktionMitKunde.getAktion();
            
            if (aktion.isErledigt()) {
                result.put("Erledigt", result.get("Erledigt") + 1);
            } else if (aktionMitKunde.istUeberfaellig()) {
                result.put("Überfällig", result.get("Überfällig") + 1);
            } else if (aktionMitKunde.istHeuteFaellig()) {
                result.put("Heute fällig", result.get("Heute fällig") + 1);
            } else {
                result.put("Anstehend", result.get("Anstehend") + 1);
            }
        }
        
        return result;
    }

    /**
     * Gibt die Anzahl der Kommunikationen nach Typ zurück.
     *
     * @return Eine Map mit dem Kommunikationstyp als Schlüssel und der Anzahl als Wert
     */
    public Map<String, Integer> getKommunikationenNachTyp() {
        Map<String, Integer> result = new HashMap<>();
        
        // Initialisiere alle Typen mit 0
        for (KommunikationsTyp typ : KommunikationsTyp.values()) {
            result.put(typ.getBezeichnung(), 0);
        }
        
        // Zähle die Kommunikationen pro Typ
        for (Kunde kunde : datenbank.getAlleKunden()) {
            for (Kommunikation kommunikation : kunde.getKommunikationen()) {
                String typ = kommunikation.getTyp();
                result.put(typ, result.getOrDefault(typ, 0) + 1);
            }
        }
        
        return result;
    }

    /**
     * Berechnet die durchschnittliche Bearbeitungszeit von Aktionen in Tagen.
     * Hinweis: Dies ist eine Beispielimplementierung, da wir keine tatsächlichen Daten
     * über den Zeitpunkt der Erledigung haben. In einer realen Anwendung würde man
     * den Zeitstempel der Erledigung speichern.
     *
     * @return Die durchschnittliche Bearbeitungszeit in Tagen oder -1, wenn keine erledigten Aktionen vorhanden sind
     */
    public double getDurchschnittlicheBearbeitungszeit() {
        List<NutzerDatenbank.AktionMitKunde> alleAktionen = datenbank.getAlleAktionen();
        List<Aktion> erledigteAktionen = alleAktionen.stream()
                .map(NutzerDatenbank.AktionMitKunde::getAktion)
                .filter(Aktion::isErledigt)
                .collect(Collectors.toList());
        
        if (erledigteAktionen.isEmpty()) {
            return -1; // Keine erledigten Aktionen
        }
        
        // In einer realen Anwendung würde man hier den tatsächlichen Zeitstempel der Erledigung verwenden
        // Für diese Beispielimplementierung nehmen wir an, dass Aktionen im Durchschnitt 2 Tage nach Fälligkeit erledigt werden
        long summe = 0;
        LocalDate heute = LocalDate.now();
        
        for (Aktion aktion : erledigteAktionen) {
            LocalDate faelligkeitsDatum = aktion.getFaelligkeitsDatum();
            // Berechne die Tage zwischen Fälligkeit und heute (als Näherung für den Erledigungszeitpunkt)
            long tage = ChronoUnit.DAYS.between(faelligkeitsDatum, heute);
            summe += tage;
        }
        
        return (double) summe / erledigteAktionen.size();
    }

    /**
     * Berechnet die Konversionsrate von Interessenten zu Bestandskunden.
     * Hinweis: Dies ist eine Beispielimplementierung, da wir keine tatsächlichen Daten
     * über Konversionen haben. In einer realen Anwendung würde man die Historie der
     * Kundenkategorien speichern.
     *
     * @return Die Konversionsrate als Prozentsatz oder -1, wenn keine Interessenten vorhanden sind
     */
    public double getKonversionsrate() {
        Map<Kundenkategorie, Integer> kundenNachKategorie = getKundenNachKategorie();
        int interessenten = kundenNachKategorie.getOrDefault(Kundenkategorie.INTERESSENT, 0);
        int bestandskunden = kundenNachKategorie.getOrDefault(Kundenkategorie.BESTANDSKUNDE, 0);
        
        if (interessenten + bestandskunden == 0) {
            return -1; // Keine Kunden
        }
        
        // In einer realen Anwendung würde man hier die tatsächliche Konversionsrate berechnen
        // Für diese Beispielimplementierung nehmen wir eine fiktive Konversionsrate an
        return (double) bestandskunden / (interessenten + bestandskunden) * 100;
    }

    /**
     * Gibt die Anzahl der Aktionen pro Kunde zurück.
     *
     * @return Eine Map mit der Kunden-ID als Schlüssel und der Anzahl der Aktionen als Wert
     */
    public Map<String, Integer> getAktionenProKunde() {
        Map<String, Integer> result = new HashMap<>();
        
        for (Kunde kunde : datenbank.getAlleKunden()) {
            result.put(kunde.getId(), kunde.getAktionen().size());
        }
        
        return result;
    }

    /**
     * Gibt die Anzahl der Kommunikationen pro Kunde zurück.
     *
     * @return Eine Map mit der Kunden-ID als Schlüssel und der Anzahl der Kommunikationen als Wert
     */
    public Map<String, Integer> getKommunikationenProKunde() {
        Map<String, Integer> result = new HashMap<>();
        
        for (Kunde kunde : datenbank.getAlleKunden()) {
            result.put(kunde.getId(), kunde.getKommunikationen().size());
        }
        
        return result;
    }

    /**
     * Berechnet den durchschnittlichen Aktivitätswert pro Kunde.
     * Der Aktivitätswert ist die Summe aus Aktionen und Kommunikationen.
     *
     * @return Der durchschnittliche Aktivitätswert oder -1, wenn keine Kunden vorhanden sind
     */
    public double getDurchschnittlicheAktivitaet() {
        List<Kunde> kunden = datenbank.getAlleKunden();
        
        if (kunden.isEmpty()) {
            return -1; // Keine Kunden
        }
        
        int summe = 0;
        
        for (Kunde kunde : kunden) {
            summe += kunde.getAktionen().size() + kunde.getKommunikationen().size();
        }
        
        return (double) summe / kunden.size();
    }
}
