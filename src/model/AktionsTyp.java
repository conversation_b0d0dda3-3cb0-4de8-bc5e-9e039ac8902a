package model;

/**
 * Enum für verschiedene Aktionstypen im CRM-System.
 */
public enum AktionsTyp {
    EMAIL("E-Mail senden"),
    ANRUF("Anrufen"),
    KUNDENGESPRAECH("Kundengespräch vereinbaren"),
    ANGEBOT("Angebot erstellen"),
    SONSTIGES("Sonstiges");

    private final String bezeichnung;

    /**
     * Konstruktor für AktionsTyp.
     *
     * @param bezeichnung Die Bezeichnung des Aktionstyps
     */
    AktionsTyp(String bezeichnung) {
        this.bezeichnung = bezeichnung;
    }

    /**
     * Gibt die Bezeichnung des Aktionstyps zurück.
     *
     * @return Die Bezeichnung des Aktionstyps
     */
    public String getBezeichnung() {
        return bezeichnung;
    }

    @Override
    public String toString() {
        return bezeichnung;
    }
}
